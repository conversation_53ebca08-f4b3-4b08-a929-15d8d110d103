import os
from math import radians, cos, sin, asin, sqrt
import pandas as pd
import json
import pickle
from pykml import parser
from sklearn.neighbors import NearestNeighbors
import requests
from urllib.parse import urlencode
import time
import pymysql
import re
from google.cloud.sql.connector import Connector, IPTypes
from google.oauth2.service_account import Credentials
import sqlalchemy
from consts import (DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, DB_TABLE_NAME,
                    CONTACT_TABLE_DICT, CONTACT_TABLE_NAME,
                    APP_SQL_GOOGLE_CREDENTIALS, SCRAPER_SQL_GOOGLE_CREDENTIALS, TODAY)


def time_it(func):
    '''
    Decorator that prints to console the Elapsed time for a function to which is applied
    '''
    def inner(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print('{0} took {1:.8f}s to execute'.format(func.__name__, execution_time))
        return result

    return inner


class OmiZone:

    def __init__(self, path_file):
        super(OmiZone, self).__init__()
        self.omi_file = path_file
        self.omi_zones = self.get_omi_zones()

    def get_omi_zones(self):
        omi_file = open(self.omi_file)
        kml_doc = parser.parse(omi_file).getroot()
        omi_file.close()

        omi_zones = dict()
        for pm in kml_doc.Document.Placemark:
            omi_zones[pm.name] = []
            geometry = [p for p in pm.getchildren() if p.tag.endswith('MultiGeometry')]
            if geometry:
                geometry = pm.MultiGeometry
                for polygon in geometry.Polygon:
                    coord_list = self.getCoordinateList(polygon)
                    for coord_pair in coord_list:
                        int_coord_pair = []
                        for c in coord_pair:
                            if c == '':
                                continue
                            int_coord_pair.append(float(c))

                        omi_zones[pm.name].append(int_coord_pair)
            else:
                for polygon in pm.Polygon:
                    coord_list = self.getCoordinateList(polygon)
                    for coord_pair in coord_list:
                        int_coord_pair = []
                        for c in coord_pair:
                            if c == '':
                                continue
                            int_coord_pair.append(float(c))
                        omi_zones[pm.name].append(int_coord_pair)
        return omi_zones

    def ray_tracing(self, x, y, poly):
        n = len(poly)
        inside = False
        p2x = 0.0
        p2y = 0.0
        xints = 0.0
        p1x, p1y = poly[0]
        for i in range(n):
            if(len(poly[i])==0):
                continue
            p2x, p2y = poly[i]
            if y > min(p1y,p2y):
                if y <= max(p1y,p2y):
                    if x <= max(p1x,p2x):
                        if p1y != p2y:
                            xints = (y-p1y)*(p2x-p1x)/(p2y-p1y)+p1x
                        if p1x == p2x or x <= xints:
                            inside = not inside
            p1x,p1y = p2x,p2y
        return inside

    def getCoordinateList(self, polygon):
        coord_list = str(polygon.outerBoundaryIs.LinearRing.coordinates).strip('\n').split(',0')
        coord_list = [p.strip().split(',') for p in coord_list]
        return coord_list

    def getOmiZone(self, longitude, latitude):
        for zone in self.omi_zones:
            if self.ray_tracing(longitude, latitude, self.omi_zones[zone]):
                return str(zone)
        return None


@time_it
def get_marketZone(database: sqlalchemy.engine.base.Engine, addressObject: pd.DataFrame) -> str:
    '''
    Gets marketZone feature for input comp that needs to be evaluated by connecting to scraping RDS db and
    assigning the marketZone of the closest house to input comp.

    Parameters
    ----------
    database: sqlalchemy.engine.base.Engine
        connection to database
    addressObject: pd.DataFrame
        pd.DataFrame containing info on input comp

    Returns
    -------
    marketZone : str
        MarketZone of the closest house to input comp.
    '''

    marketZone = 'santa rita'
    with database.connect() as conn:
        query = sqlalchemy.text(
            f'SELECT city, marketZone, latitude, longitude FROM {DB_TABLE_NAME} WHERE city like :city AND latitude IS NOT NULL AND longitude IS NOT NULL ORDER BY lastSeenDate DESC LIMIT 10000;')
        fetchmany = conn.execute(query, parameters={"city": addressObject.at[0, 'city']}).fetchall()
        if len(fetchmany) > 0:
            # compute closest house's index
            dataset = pd.DataFrame(fetchmany)
            # select only valid marketZones
            marketZone_counts = pd.DataFrame(dataset['marketZone'].value_counts())
            marketZone_list = list(marketZone_counts.loc[marketZone_counts['count'] > 10].index)
            dataset.drop(dataset.loc[~dataset['marketZone'].isin(marketZone_list)].index, inplace=True)
            dataset = dataset.reset_index(drop=True)
            # get coordinates for neighbors
            coordinates = dataset[['latitude', 'longitude']].values
            nbrs = NearestNeighbors(n_neighbors=1, algorithm='ball_tree').fit(coordinates)
            distance, index = nbrs.kneighbors([[float(addressObject.at[0, 'latitude']), float(addressObject.at[0, 'longitude'])]])
            nearest_house_index = index[0][0]
            # assign to input comp marketZone of the closest house
            marketZone = dataset.loc[int(nearest_house_index), 'marketZone']
    return marketZone.capitalize()


@time_it
def get_zone_info(marketZone, zoneinfo_file):
    zone_info = {}
    try:
        if isinstance(marketZone, str) and marketZone != "":
            marketZone = marketZone.lower()
            # Open zone info file
            zone_info_file = pd.read_csv(zoneinfo_file)
            avgSalePrice = zone_info_file.loc[zone_info_file['marketZone'].str.lower() == marketZone, 'avgPriceRen'].iloc[0]
            avgSaleTime = zone_info_file.loc[zone_info_file['marketZone'].str.lower() == marketZone, 'avgSaleTimeRen'].iloc[0]
            avgSaleTime = int(avgSaleTime) // 30
            avgSaleTime = f'{avgSaleTime} mesi' if avgSaleTime > 1 else f'{avgSaleTime} mese'
            # Select the marketZone required
            zone_info_file = zone_info_file.loc[zone_info_file['marketZone'].str.lower() == marketZone]
            zone_info_file = zone_info_file.reset_index(drop=True)
            zone_info_file = zone_info_file.iloc[0]

            zone_info = {
                'attrattivita': float(zone_info_file.attractiveness),
                'prezzo_medio_mq': str(avgSalePrice),
                'tempo_medio_vendita': avgSaleTime,
                'supermercati': str(zone_info_file.markets),
                'scuole': str(zone_info_file.schools),
                'parchi': str(zone_info_file.parks),
                'rifornitori': str(zone_info_file.refuelers),
                'farmacie': str(zone_info_file.pharmacies),
            }
    except Exception as e:
        print('Error in get_zone_info(): ', e)

    return zone_info


@time_it
def get_house_info(planimetry, maintenance_status):
    pros = []
    cons = []
    value = 0

    # Check terrace
    if bool(planimetry.at[0, 'hasTerrace']):
        pros.append('Presenza di terrazzo')
        value += 9

    # Check Status
    if "nuovo" in maintenance_status.lower() or "ottimo" in maintenance_status.lower():
        pros.append('Ottime condizioni interne')
        value += 9
    else:
        cons.append("Condizioni interne da rivedere")

    # Check floor
    if planimetry.at[0, 'floor'] == "Seminterrato" or planimetry.at[0, 'floor'] == "Rialzato" or planimetry.at[0, 'floor'] == "Terra":
        cons.append("Piano basso")
    elif int(planimetry.at[0, 'floor']) <= 1:
        cons.append("Piano basso")
    else:
        if bool(planimetry.at[0, 'hasElevator']):
            pros.append('Piano medio/alto con ascensore')
            value += 8
        else:
            cons.append('Piano medio/alto senza ascensore')
        if int(planimetry.at[0, 'floor']) >= 6:
            pros.append('La casa gode di un\'ottima vista')
            value += 5

    # Check private garden
    if bool(planimetry.at[0, 'hasPrivateGarden']):
        pros.append('Presenza di giardino privato')
        value += 9

    # Check bathrooms
    if int(planimetry.at[0, 'surface']) >= 100:
        if int(planimetry.at[0, 'bathrooms']) <= 1:
            cons.append("Un solo servizio")
        elif int(planimetry.at[0, 'bathrooms']) == 2:
            pros.append("Doppi servizi")
            value += 8
        elif int(planimetry.at[0, 'bathrooms']) == 3:
            pros.append("Tripli servizi")
            value += 8
        elif int(planimetry.at[0, 'bathrooms']) > 3:
            pros.append("Servizi multipli")
            value += 8
    else:
        if int(planimetry.at[0, 'bathrooms']) <= 1:
            value += 8
        elif int(planimetry.at[0, 'bathrooms']) == 2:
            pros.append("Doppi servizi")
            value += 8
        elif int(planimetry.at[0, 'bathrooms']) == 3:
            pros.append("Tripli servizi")
            value += 8
        elif int(planimetry.at[0, 'bathrooms']) > 3:
            pros.append("Servizi multipli")
            value += 8

    # Check exposition
    if len(planimetry.at[0, 'exposition']) > 1:
        value += 6
        if len(planimetry.at[0, 'exposition']) == 2:
            pros.append('Doppia esposizione')
        elif len(planimetry.at[0, 'exposition']) == 3:
            pros.append('Tripla esposizione')
        else:
            pros.append('Esposizione quadrupla')
    elif len(planimetry.at[0, 'exposition']) == 1:
        cons.append('Esposizione singola')

    # Check garage
    if bool(planimetry.at[0, 'hasGarage']):
        pros.append('Presenza di garage')
        value += 3

    # Check ripostiglio
    if bool(planimetry.at[0, 'hasCloset']):
        pros.append('Presenza di ripostiglio')
        value += 4
    else:
        cons.append('Assenza di ripostiglio')

    # Check shared garden
    if bool(planimetry.at[0, 'hasSharedGarden']):
        pros.append('Presenza di giardino condominiale')
        value += 3

    # Check concierge
    if bool(planimetry.at[0, 'hasConcierge']):
        pros.append('Presenza di portineria')
        value += 3
    else:
        cons.append("Assenza di portineria")

    # Check cantina
    if bool(planimetry.at[0, 'hasCantina']):
        value += 6
    else:
        cons.append("Assenza di cantina")

    # Produce output
    if len(cons) == 0:
        cons.append('Non abbiamo riscontrato dei fattori che influenzano negativamente questo immobile')

    if len(pros) > 4:
        pros = pros[:4]
    if len(cons) > 4:
        cons = cons[:4]

    value = (value * 100) // 70
    house_info = {
        'pro': pros,
        'contro': cons,
        'indice': value
    }
    return house_info


@time_it
def get_similar_ads(database: sqlalchemy.engine.base.Engine, original_data: pd.DataFrame, predicted_price: float) -> list[dict]:
    '''
    Taking a comp as input, returns a list of ads found in db to be similar to comp in input. List lenght is 4 at maximum.
    Similarity is defined as:
    Same city
    Same marketZone
    Same mainentanceStatus
    grossSquareFootage in a 10% range
    pricePsm in a 10% range.

    Parameters
    ----------
    database: sqlalchemy.engine.base.Engine
        db making the connection
    original_data: pd.DataFrame
        input comp
    predicted_price: float
        predicted price of comp

    Returns
    -------
    similar_ads: list[dict]
        list of dictionaries of similar comps
    '''
    similar_ads = []
    user_data = original_data.iloc[0]
    predicted_ppsm = int(float(predicted_price) // float(user_data.grossSquareFootage))
    # Connect to db and find similar ads
    # connecting to db
    with database.connect() as conn:
        query = sqlalchemy.text(
            f'SELECT * FROM {DB_TABLE_NAME} WHERE city=:city AND marketZone=:marketZone AND maintenanceStatus=:maintenanceStatus AND grossSquareFootage BETWEEN :min_gSF AND :max_gSF AND pricePsm BETWEEN :min_psm AND :max_psm ORDER BY lastSeenDate desc LIMIT 50;')
        params = {"city": user_data.city,
                    "marketZone": user_data.marketZone,
                    "maintenanceStatus": user_data.maintenanceStatus,
                    "min_gSF": user_data.grossSquareFootage * 0.9,
                    "max_gSF": user_data.grossSquareFootage * 1.1,
                    "min_psm": predicted_ppsm * 0.9,
                    "max_psm": predicted_ppsm * 1.1}
        fetchmany = conn.execute(query, parameters=params).fetchall()
        # if no results widen percentage range
        if len(fetchmany) == 0:
            params["min_gSF"] = user_data.grossSquareFootage * 0.8
            params["max_gSF"] = user_data.grossSquareFootage * 1.2
            params["min_psm"] = predicted_ppsm * 0.8
            params["max_psm"] = predicted_ppsm * 1.2
            fetchmany = conn.execute(query, parameters=params).fetchall()
        # if no results found widen gSF, pricePsm range and try again with no maintenanceStatus filter
        if len(fetchmany) == 0:
            del params["maintenanceStatus"]
            query = sqlalchemy.text(str(query).replace(' AND maintenanceStatus=:maintenanceStatus', ''))
            fetchmany = conn.execute(query, parameters=params).fetchall()
    # compute similarity
    fetchmany_df = pd.DataFrame(fetchmany)
    fetchmany_df.insert(len(fetchmany_df), 'similarity', 50)
    for idx, fetch in fetchmany_df.iterrows():
        similarity = 99
        similarity_cols = ['propertyType', 'rooms', 'numberOfBathrooms', 'energyClass']
        similarity_weight = 39 / len(similarity_cols)
        for col in similarity_cols:
            if fetch[col] != user_data[col]:
                similarity -= similarity_weight
        fetch['similarity'] = int(similarity)
    if len(fetchmany_df) == 0:
        similar_ads = [{}]
    else:
        # choose 4 most similar ads to be send after checking no NaNs among interesting features are sent
        fetchmany_df = fetchmany_df.sort_values(by='similarity', ascending=False)
        similar_ads_counter = 0
        for idx, row in fetchmany_df.iterrows():
            if pd.isna(row[['streetName', 'latitude', 'longitude', 'propertyType', 'grossSquareFootage', 'price', 'pricePsm', 'rooms', 'similarity']]).sum() < 1:
                similar_ads_counter += 1
                similar_ads.append({
                    'address': str(row['streetName']),
                    'latitude': row['latitude'],
                    'longitude': row['longitude'],
                    'tipo': row['propertyType'],
                    'mq': row['grossSquareFootage'],
                    'prezzo': row['price'],
                    'prezzo_mq': row['pricePsm'],
                    'locali': row['rooms'],
                    'somiglianza': f'{row.similarity}%'})
            else:
                continue
            if similar_ads_counter > 3:
                break
    return similar_ads


def get_extra_evaluation(planimetry, result):
    extra = 0.0
    if bool(planimetry.at[0, 'hasGarage']):
        extra += 25000 * int(planimetry.at[0, 'garageCount'])
    if bool(planimetry.at[0, 'hasConcierge']):
        extra += result * 0.05
    if not bool(planimetry.at[0, 'hasCloset']):
        extra -= result * 0.05
    if not bool(planimetry.at[0, 'hasCantina']):
        extra -= result * 0.10
    if bool(planimetry.at[0, 'hasBalcony']):
        if int(planimetry.at[0, 'numBalcony']) == 3:
            extra += result * 0.05
        elif int(planimetry.at[0, 'numBalcony']) == 4:
            extra += result * 0.10
    if bool(planimetry.at[0, 'knowExposition']):
        if len(planimetry.at[0, 'exposition']) == 1:
            extra -= result * 0.04
        elif len(planimetry.at[0, 'exposition']) == 2:
            extra -= result * 0.03
        elif len(planimetry.at[0, 'exposition']) == 3:
            extra += result * 0.05
        elif len(planimetry.at[0, 'exposition']) == 4:
            extra += result * 0.10
    if planimetry.at[0, 'heating'] == "Autonomo":
        if planimetry.at[0, 'termovalvole'] is True:
            extra += result * 0.03
        elif planimetry.at[0, 'termovalvole'] is False:
            extra += result * 0.01
        else:
            extra += result * 0.02
    elif planimetry.at[0, 'heating'] == "Centralizzato":
        if planimetry.at[0, 'termovalvole'] is True:
            extra += result * 0.05
        elif planimetry.at[0, 'termovalvole'] is False:
            extra += result * 0.03
        else:
            extra += result * 0.04
    elif planimetry.at[0, 'heating'] == "Assente":
        extra += result * 0.04

    return extra


def dots_as_thousands_separator(num: float) -> str:
    '''
    Method that converts an input float :code:`num` into an integer and returns its string
    representation with dots as thousands separators after being truncated to the thousands.
    Needed to format house prices the italian way.

    Parameters
    ----------
    num: float
        float representing the value to be converted

    Returns
    -------
    str_num: str
        string version of :code:`num` float as integer with dots as thousands separators
    '''
    
    try:
        # truncate to thousands
        decimals = -3 # decimals = 2: truncate to second decimal place
        multiplier = 10 ** decimals
        approx_num = int(int(num * multiplier)/multiplier)
        # formatting number as comma separated
        comma_separated_num = '{:,}'.format(approx_num)
        # changing commas in dots
        dot_separated_num = comma_separated_num.replace(',', '.')

    except Exception as e:
        print(e)
        dot_separated_num = 'argument num of add_thousands_separator() is invalid'

    return dot_separated_num


def number_of_points_of_interest_in_radius(lat: float, long: float, radius: int, api_key: str) -> dict:
    '''
    Extracts number of points of interest in radius from a certain latitude and longitude input position.
    Points of interest are searched through Places API and filtered on conditions so to include only relevant
    points of interest.
    Conditions for the business to be taken into consideration is if in its name at least one of the words
    in key_words dict appears for the placetype we are looking for.

    Connects to Google's Places API, remember NEWARC is paying!.

    Parameters
    ----------
    lat, long: float
        latitude and longitude values
    radius: int
        radius in meters for the research
    api_key: str
        unlocked key for Places API

    Returns
    -------
    result: dict
        dictionary with placetypes as keys and number of selected placetypes in radius as values

    '''
    endpoint = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json'
    result = dict()
    placetypes = ['supermarket', 'school', 'park', 'gas_station', 'pharmacy']
    key_words = {'supermarket': ['supermercato','supermarket',
                                 'aldi', 'action', 'auchan', 'bennet', 'borello',
                                 'carrefour', 'conad', 'coop', 'crai', 'despar', 'dico', 'doro',
                                 'esselunga', 'eurospin',
                                 'famila',
                                 'gigante',
                                 'in\'s',
                                 'ld', 'lidl',
                                 'pam', 'penny',
                                 'simply',
                                 'u2', 'unes'],
                 'school': ['scuola primaria',
                            'scuola secondaria',
                            'scuola superiore',
                            'scuola elementare',
                            'scuola materna',
                            'scuola media',
                            'liceo',
                            'università',
                            'university',
                            'politecnico'
                            'asilo',
                            'elementary school',
                            'istituto comprensivo', 'istituto superiore'
                            'i.p.s.i.a.', 'iis', 'itis'],
                 'park': ['giardino', 'giardini',
                          'area verde',
                          'lungo'
                          'parco', 'parchi',
                          'monumento'],
                 'pharmacy': ['farmacia',
                              'pharmacy']}

    for placetype in placetypes:
        params = {'location': f'{lat}, {long}',
                  'radius': radius,
                  'type': placetype,
                  'key': api_key}
        url_params = urlencode(params)
        url = f'{endpoint}?{url_params}'

        try:
            res = requests.get(url)
            res_json = res.json()
        except Exception as e:
            res_json = {'results': [{'error':{'message': 'API call failed'}}]}
            print(e)

        shops_found = res_json['results']

        # Get name of active shops if shop has name and business status not CLOSED_PERMANENTLY.
        # if no business status is given but shop has a name consider it as acive and get name
        # if no name is available do not consider
        names_of_active_shops = []
        for shop_dict in shops_found:
            if 'name' in list(shop_dict.keys()):
                if 'business_status' in list(shop_dict.keys()):
                    if shop_dict['business_status'] != 'CLOSED_PERMANENTLY':
                        names_of_active_shops.append(shop_dict['name'])
                else:
                    names_of_active_shops.append(shop_dict['name'])

        # Get name of active shops whose name satisfies condition on placetype.
        # If placetypes have no conditions (expressed with key_words dict) simply consider all.
        names_of_selected_placetypes = []
        if placetype in list(key_words.keys()):
            for shop_name in names_of_active_shops:
                is_shop_satisfing_condition = False
                for word in key_words[placetype]:
                    if word in shop_name.lower():
                        is_shop_satisfing_condition = True
                if is_shop_satisfing_condition:
                    names_of_selected_placetypes.append(shop_name)
        else:
            names_of_selected_placetypes += names_of_active_shops

        result[placetype] = len(names_of_selected_placetypes)

    return result


def haversine_distance(lat1: float, long1: float, lat2: float, long2: float) -> float:
    """
    Calculate the great circle distance between two points
    on a sphere where coordinates of the points are expressed as latitude and longitude (specified in decimal degrees).

    Please mind the limitations of the haversine method!

    Parameters
    ----------
    lat1: float
        latitude of first input point
    long1: float
        longitude of first input point
    lat2: float
        latitude of second input point
    long2: float
        longitude of second input point

    Returns
    -------
    km: float
        distance between two points in kilometers
    """
    # convert decimal degrees to radians
    lat1, long1, lat2, long2 = map(radians, [lat1, long1, lat2, long2])
    # haversine formula
    dlon = long2 - long1
    dlat = lat2 - lat1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * asin(sqrt(a))
    # Radius of earth in kilometers is 6371
    km = 6371 * c

    return km


def connect_mysql_db(host, user, password, port) -> tuple:
    '''
    Connects to MySQL db thanks to pymysql library.

    Parameters
    ----------
    host: str
        name of host server
    user: str
        allowed user
    password: str
        user's password
    port: int
        port to use for connection

    Returns
    -------
    connection: pymysql.connections.Connection object
        connection object
    cursor: pymysql.cursors.DictCursor object
        cursor object
    '''
    try:
        connection = pymysql.connect(host=host,
                                     user=user,
                                     password=password,
                                     port=port)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
    except Exception as e:
        connection, cursor = None, None
        print('Error in connect_mysql_db():')
        print(e)
    return connection, cursor


def connect_with_connector(connection_name: str, user: str, passwd: str, db_name: str, creds: str) -> sqlalchemy.engine.base.Engine:
    """
    Initializes a connection pool for a Cloud SQL instance of MySQL.

    Uses the Cloud SQL Python Connector package.

    Parameters
    ----------
    connection_name: str
        Name of the Cloud SQL instance, in the form "project:region:instance".
    user: str
        Username to use when connecting to the database.
    passwd: str
        Password to use when connecting to the database.
    db_name: str
        Name of the database to connect to.
    creds: str
        str representation of Google credential JSON.

    Returns
    -------
    pool: sqlalchemy.engine.base.Engine
        Connection pool.
    """
    # Note: Saving credentials in environment variables is convenient, but not
    # secure - consider a more secure solution such as
    # Cloud Secret Manager (https://cloud.google.com/secret-manager) to help
    # keep secrets safe.

    ip_type = IPTypes.PUBLIC
    # credentials = json.loads(creds)
    credentials = creds
    credentials = Credentials.from_service_account_info(credentials)
    connector = Connector(ip_type, credentials=credentials)

    def getconn() -> pymysql.connections.Connection:
        conn: pymysql.connections.Connection = connector.connect(
            connection_name,
            "pymysql",
            user=user,
            password=passwd,
            db=db_name,
        )
        return conn

    pool = sqlalchemy.create_engine(
        "mysql+pymysql://",
        creator=getconn,
        # Pool size is the maximum number of permanent connections to keep.
        pool_size=5,
        # Temporarily exceeds the set pool_size if no connections are available.
        max_overflow=2,
        # The total number of concurrent connections for your application will be
        # a total of pool_size and max_overflow.
        # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
        # new connection from the pool. After the specified amount of time, an
        # exception will be thrown.
        pool_timeout=30,  # 30 seconds
        # 'pool_recycle' is the maximum number of seconds a connection can persist.
        # Connections that live longer than the specified amount of time will be
        # re-established
        pool_recycle=600,  # 10 minutes
    )
    return pool


def disconnect_mysql_db(connection, cursor):
    '''
    Disconnects to MySQL db thanks to pymysql library.

    Parameters
    ----------
    connection: pymysql.connections.Connection object
        connection object
    cursor: pymysql.cursors.DictCursor object
        cursor object
    '''
    try:
        cursor.close()
        connection.close()
    except Exception as e:
        print('Error in disconnect_mysql_db():')
        print(e)


def use_db_query(cursor, db_name: str) -> None:
    '''
    Send to db 'use db_name' query. Connection with db must be established already.

    Parameters
    ----------
    cursor: pymysql.cursors.Cursor object
        cursor object
    db_name: str
        name of the db we want to use
    '''
    try:
        query = f'USE {db_name};'
        cursor.execute(query)
    except Exception as e:
        print('Error in use_db_query():')
        print(e)


def custom_select_query(cursor, query: str, query_params: tuple) -> list[dict]:
    '''
    Runs custom SELECT query on db. Returns list of dicts with comps. Connection to db must be already set up.
    If we want to use parameters in the query, use %s instead of writing the actual value in the query string,
    query_params will be substituted instead of %s in the query on appearance order.
    WATCHOUT:
    eg: "SELECT * FROM %s WHERE ...", with query_params = ('my_table'), raises MySQL syntax error because the string
    becomes "SELECT * FROM 'my_table' WHERE ..." instead of "SELECT * FROM my_table WHERE ...". Use f-strings instead.

    Parameters
    ----------
    cursor: pymysql.cursors object
        cursor object
    query: str
        query we want to run
    query_params: tuple
        params we want to insert in the query

    Returns
    -------
    fetchall: list(dict)
        list of dictionaries with feature names as keys and respective values as values
    '''
    try:
        cursor.execute(query, query_params)
        fetchall = cursor.fetchall()
    except Exception as e:
        print('Error in custom_select_query():')
        print(e)
        fetchall = [{}]
    return fetchall


def load_json_file(file_path: str) -> dict:
    '''
    Loads a json file and returns a dictionary.

    Parameters
    ----------
    file_path: str
        path to the file

    Returns
    -------
    data: dict
        dictionary with the data from the file
    '''
    with open(file_path, 'r') as f:
        data = json.load(f)
    return data


def encoding(df: pd.DataFrame, options) -> pd.DataFrame:
    '''
    Encodes data of comparable under evaluation according to rules defined during training procedure

    Parameters
    ----------
    df: pd.DataFrame
        dataframe containing info on comparable under evaluation
    options: NameSpace
        options defined in argparser object

    Returns
    -------
    df: pd.DataFrame
        dataframe with encoded info on comparable under evaluation
    '''
    # CATEGORICAL VARS HANDLING
    data_path = f'./model/{options.training_city}/'
    # Define mapping for ordinal categorical vars
    ordinal_values_mapping = load_json_file(f'{data_path}ordinal_values_mapping.json')
    data_types_dict = dict()
    # Encode categorical vars
    for col in df[options.valid].select_dtypes('O').columns:
        data_types_dict[col] = 'category'
        if col in ordinal_values_mapping.keys():
            df.loc[:, col] = df[col].map(ordinal_values_mapping[col])
        else:
            # retrieve file paths
            file_path = ''
            for file in os.listdir(data_path):
                if re.match(r'.*encoder\.pkl', file):
                    file_path = data_path + file
            encoder = pickle.load(open(file_path, 'rb'))
            if col == 'marketZone':
                df.loc[:, col] = encoder.transform(df[col].str.lower())
            else:
                df.loc[:, col] = encoder.transform(df[col])
    # Change categorical cols dtype
    df = df.astype(data_types_dict)
    return df


def my_unitFloor(unit_floor):
    if unit_floor < 0:
        return -1
    elif 0 <= unit_floor < 1:
        return 0
    elif 1 <= unit_floor < 2:
        return 1
    elif 2 <= unit_floor < 3:
        return 2
    elif 3 <= unit_floor < 4:
        return 3
    elif 4 <= unit_floor < 5:
        return 4
    elif unit_floor >= 5:
        return 5


def unitFloor_sorting_key(floor):
    # Definizione della mappa di priorità per i piani non numerici
    priority_map = {
        'piano rialzato': -0.25,
        'ammezzato': -0.5,
        'piano terra': 0,
        'seminterrato': 0.5
    }

    # Se il valore è un numero, convertilo e usa l'inverso per ordinamento decrescente
    if floor.isdigit():
        return -int(floor)  # Ordinamento dal numero più grande al più piccolo

    # Altrimenti, assegna la priorità dalla mappa
    return priority_map.get(floor, float('inf'))


def update_contacts_db(contact: dict, firebaseId: str, city: str):
    try:
        db = connect_with_connector(DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, SCRAPER_SQL_GOOGLE_CREDENTIALS)
        # connect to db
        with db.connect() as conn:
            # create db if it doesn't exist
            query = f'CREATE DATABASE IF NOT EXISTS {DB_NAME}; '
            conn.execute(sqlalchemy.text(query))
            conn.commit()
            # select db
            query = f'USE {DB_NAME}; '
            conn.execute(sqlalchemy.text(query))
            conn.commit()
            # create table if it doesn't exist
            table_def_string = ''
            for key in CONTACT_TABLE_DICT.keys():
                table_def_string += key + ' ' + CONTACT_TABLE_DICT[key] + ','
            table_def_string = table_def_string.strip(',')
            query = f'CREATE TABLE IF NOT EXISTS {CONTACT_TABLE_NAME} ({table_def_string}); '
            conn.execute(sqlalchemy.text(query))
            conn.commit()
            # add row to table
            not_id_table_cols = [key for key in CONTACT_TABLE_DICT.keys() if key != 'id']
            all_cols_str = ', '.join(not_id_table_cols)
            str_formatting_list = [f':{key}' for key in not_id_table_cols]
            str_formatting = ', '.join(str_formatting_list)
            insert_into_query = f'''
                                INSERT INTO {CONTACT_TABLE_NAME} ({all_cols_str}) VALUES ({str_formatting})
                                '''
            params = dict()
            for key in not_id_table_cols:
                if key in contact.keys():
                    params[key] = contact[key]
                elif key == 'firebaseId':
                    params[key] = firebaseId
                elif key == 'city':
                    params[key] = city
                elif key == 'insertionDate':
                    params[key] = TODAY
                else:
                    print('Warning in update_contacts_db():')
                    print(f'Key {key} not found in input contact dict.')
                    params[key] = None
            conn.execute(sqlalchemy.text(insert_into_query), params)
            conn.commit()
    except Exception as e:
        print('Error in update_contacts_db():')
        print(e)



def parse_request_to_dfs(req: dict) -> tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    '''
    Parses request from frontend to pandas dataframes

    Parameters
    ----------
    req: dict
        request from frontend

    Returns
    -------
    tuple: tuple of pd.DataFrame
        pandas dataframe list with request info
    '''
    data = pd.DataFrame(req['comparabili'])
    address = pd.DataFrame([req['addressObject']])
    planimetry = pd.DataFrame([req['planimetry']])
    evaluation_city = address.at[0, 'city']
    # Include omiZone
    omiZone = OmiZone(f"data/{evaluation_city}/{evaluation_city}_OmiZones_2022.kml")
    data.insert(loc=len(data.columns), column="omiZone", value="")
    data.at[0, 'omiZone'] = omiZone.getOmiZone(data.at[0, 'longitude'], data.at[0, 'latitude'])
    # Parse maintenanceStatus
    if data.at[0, 'maintenanceStatus'] == 'Ottimo/Ristrutturato':
        data.at[0, 'maintenanceStatus'] = 'Ottimo / Ristrutturato'
    # Parse contructionYear and energyClass
    if planimetry.at[0, 'unknownBuiltYear']:
        data.at[0, 'constructionYear'] = 1960
        data['constructionYear'] = data['constructionYear'].astype(int)
    if data.at[0, 'energyClass'] is None or data.at[0, 'energyClass'] == "None" or data.at[
        0, 'energyClass'] == 'null' or data.at[0, 'energyClass'] == '':
        data.at[0, 'energyClass'] = "E"
    if data.at[0, 'energyClass'] == "Non classificabile" or data.at[0, 'energyClass'] == "In attesa" or data.at[
        0, 'energyClass'] == "Non so":
        data.at[0, 'energyClass'] = "E"
    # Parse UnitFloor
    if data.at[0, 'unitFloor'] == "Seminterrato":
        data.at[0, 'unitFloor'] = "-0.5"
    elif data.at[0, 'unitFloor'] == "Terra":
        data.at[0, 'unitFloor'] = "0.0"
    elif data.at[0, 'unitFloor'] == "Rialzato":
        data.at[0, 'unitFloor'] = "0.25"
    data['unitFloor'] = data['unitFloor'].apply(pd.to_numeric, downcast='float', errors='coerce')
    data['unitFloor'] = data['unitFloor'].apply(my_unitFloor)
    # Parse gardenType
    if pd.isna(planimetry.at[0, 'hasPrivateGarden']):
        planimetry.at[0, 'hasPrivateGarden'] = False
    if planimetry.at[0, 'hasPrivateGarden']:
        if planimetry.at[0, 'hasSharedGarden']:
            data.at[0, 'gardenType'] = 'giardino privato e comune'
        else:
            data.at[0, 'gardenType'] = 'giardino privato'
    elif planimetry.at[0, 'hasSharedGarden']:
        data.at[0, 'gardenType'] = 'giardino comune',
    else:
        data.at[0, 'gardenType'] = 'nessuno'
    # Parse furnished, kitchenType
    if data.at[0, 'furnished'] == "":
        data.at[0, 'furnished'] = 'non arredato'
    if data.at[0, 'kitchenType'] == "":
        data.at[0, 'kitchenType'] = 'cucina abitabile'
    print('parsing finished!')
    return data, address, planimetry


def compute_price_correction(city, zone, status, elev) -> float:
    '''
    Compute price correction based on city, zone, status and elevator.

    Parameters
    ----------
    city: str
        city name
    zone: str
        zone name
    status: str
        maintenance status
    elev: bool
        elevator availability
    '''
    std_corrections = {'Torino': 150,
                       'Roma': 300,
                       'Milano': 500,
                       'Trieste': 200}
    city = city.capitalize()
    zone = zone.lower()
    # Search for a corrections file in model/city folder
    corrections_file_path = f'model/{city.capitalize()}/'
    for file in os.listdir(corrections_file_path):
        if re.match('.*corrections.*', file):
            corrections_file_path += file
    # If correction file not found apply standard correction
    if not re.match('.*corrections.*', corrections_file_path):
        correction = std_corrections[city.capitalize()]
    # Else apply correction
    else:
        try:
            corrections_dict = load_json_file(corrections_file_path)
            if elev:
                correction = corrections_dict[city][zone][status]['true']
            else:
                correction = corrections_dict[city][zone][status]['false']
        except Exception as e:
            print(f'error in apply_price_correction():')
            correction = std_corrections[city.capitalize()]
    return correction

