import pandas as pd
import json
from bs4 import BeautifulSoup
import requests
import re
import time
from datetime import date
import argparser
import utils
from scraping_immobiliare import random_sleep
from consts import (DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME,
                    VENDUTO_TABLE_NAME, VENDUTO_TABLE_DICT, SCRAPER_SQL_GOOGLE_CREDENTIALS)
import sqlalchemy

TODAY = date.today().strftime('%Y/%m/%d')

scraping_db = None
def init_db_connection() -> None:
    """Initiates connection to database and its' structure."""
    global scraping_db
    if scraping_db is None:
        scraping_db = utils.connect_with_connector(DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, SCRAPER_SQL_GOOGLE_CREDENTIALS)

@utils.time_it
def get_agency_links(city: str) -> list:
    """
    Scrapes the list of links to agencies in a given city according to immobiliare website.

    Parameters
    ----------
    city: str
        name of the city we want to find agencies in

    Returns
    -------
    agency_links: str
        list of agency's links.
    """

    # scrape list of links to agencies
    agency_links = list()
    city = city.lower()
    page_number = 0
    agencies_in_city_url = f'https://www.immobiliare.it/agenzie-immobiliari/{city}/'
    print(f'Starting fetching agency links in {city.capitalize()}')
    while True:
        try:
            page_number += 1
            if page_number != 1:
                agencies_in_city_url = f'https://www.immobiliare.it/agenzie-immobiliari/{city}/?pag={page_number}'
            random_sleep()
            # # uncomment for faster computational time (only one page is fetched)
            # if page_number > 2:
            #     break
            if page_number%10 == 0:
                print('agency pages:', page_number)
            res = requests.get(agencies_in_city_url)
            soup = BeautifulSoup(res.text, "html.parser")
            # check if last page was reached
            data = soup.find('div', class_='nd-alert nd-alert--warning in-errorPage__alert in-errorPage__title')
            if data:
                if re.match('.*non è più disponibile.*', data.text.lower()):
                    # last page reached, stop scraping
                    # print('last agency page:', page_number)
                    # print('end of pages')
                    break
            # get agency links
            data = soup.find('ul', class_='nd-list nd-list--borderBottom sl-agencyList__items')
            if data:
                for d in data:
                    agency_url = d.find('a', class_='in-agencyTitle')['href']
                    agency_links.append(agency_url)
        except Exception as e:
            print(f'Error in get_agency_links() method:\n{e}')

    return agency_links


def get_agency_venduto_data_id(agency_url: str) -> list:
    """
    Retrieves the immobiliare IDs of properties marked as 'Venduto' (sold) from the given agency URL.

    Parameters
    ----------
    agency_url: str
        the URL of the agency's website.

    Returns
    -------
    immobiliare_ids: list
        a list of immobiliare IDs of sold properties.
    """

    page_number = 0
    immobiliare_ids = list()
    try:
        url = str(agency_url)
        page_number = 1
        while True:
            # compose link and send request after sleep time
            if page_number == 1:
                adj_link = url + f'?contractId=15&categoryId=1&sortBy=dataModifica&sortDir=desc'
            else:
                adj_link = url + f'?contractId=15&categoryId=1&sortBy=dataModifica&sortDir=desc&pag={page_number}'
            page_number += 1
            random_sleep()
            res = requests.get(adj_link)
            # parse html
            soup = BeautifulSoup(res.text, "html.parser")
            # check if venduto page is found
            data = soup.find('p', class_='in-emptyState__title')
            if data:
                if re.match('.*oops.*', data.text.lower()):
                    break
            # check if last page was reached
            data = soup.find('div', class_='nd-alert nd-alert--warning in-errorMessage__alert in-errorMessage__title')
            if data:
                if re.match('.*non è più disponibile.*', data.text.lower()):
                    # print('page_number:', page_number)
                    # print('end of pages')
                    break
            # # find all links with venduto tag (exclude affitto)
            # data = soup.find('ul', class_='nd-list in-searchLayoutList')
            # if len(data) > 0:
            #     for d in data:
            #         vendutoAdId = d.findChildren()[0].get('id')
            #         vendutoTag = d.find('div', class_='nd-badge nd-badge--promotion nd-badge--topRight in-listingPhotos__badge')
            #         if re.match('.*venduto.*', vendutoTag.text):
            #             immobiliare_ids.append(vendutoAdId)
            magic_script = soup.find('script', id="__NEXT_DATA__")
            data_dict = json.loads(magic_script.text)
            data_dict = data_dict['props']['pageProps']['dehydratedState']['queries'][1]['state']['data']['listing']
            if len(data_dict) > 0:
                for d in data_dict:
                    # condizione venduto
                    if d['realEstate']['status'] == 'sold':
                        immobiliare_ids.append(d['realEstate']['id'])
    except Exception as e:
        print(f'Error in get_agency_venduto_data_id() method:\n{e}')
        time.sleep(5)
    return immobiliare_ids


def save_to_db_mysql(data: dict):
    """
    Saves the given dictionary of data to a MySQL database.

    Parameters
    ----------
    data: dict
        a dictionary of data to be saved.
    """

    # connecting to db
    try:
        with scraping_db.connect() as conn:
            # creating db if it doesn't exist
            create_db_query = f'''CREATE DATABASE IF NOT EXISTS {DB_NAME};'''
            conn.execute(sqlalchemy.text(create_db_query))
            conn.commit()
            # select db
            use_db_query = f'USE {DB_NAME}'
            conn.execute(sqlalchemy.text(use_db_query))
            conn.commit()
            # create table if it doesn't exist
            table_def_string = ''
            for key in VENDUTO_TABLE_DICT.keys():
                table_def_string += key + ' ' + VENDUTO_TABLE_DICT[key] + ','
            table_def_string = table_def_string.strip(',')
            create_table_query = f'''CREATE TABLE IF NOT EXISTS {VENDUTO_TABLE_NAME} ({table_def_string});'''
            conn.execute(sqlalchemy.text(create_table_query))
            conn.commit()
            # find out if it's already on db
            is_duplicate_query = f'''SELECT * FROM {VENDUTO_TABLE_NAME} WHERE siteId = :siteId;'''
            duplicates = conn.execute(sqlalchemy.text(is_duplicate_query), parameters={'siteId': data.get('siteId')}).fetchall()
            duplicates = pd.DataFrame(duplicates).to_dict(orient='records')
            # if already on db update lastSeenDate and timesSeen
            if len(duplicates) != 0:
                old_comp = duplicates[0]
                old_comp['lastSeenDate'] = TODAY
                old_comp['timesSeen'] += 1
                # update old row with new information
                update_query = f'''UPDATE {VENDUTO_TABLE_NAME} 
                                   SET lastSeenDate = :lastSeenDate, 
                                       timesSeen = :timesSeen 
                                   WHERE id = :id;'''
                params = {'lastSeenDate': old_comp['lastSeenDate'],
                          'timesSeen': old_comp['timesSeen'],
                          'id': old_comp['id']}
                conn.execute(sqlalchemy.text(update_query), parameters=params)
                conn.commit()
            # else add row to db
            else:
                all_cols = [key for key in VENDUTO_TABLE_DICT.keys() if key != 'id']
                all_cols_str = ', '.join(all_cols)
                str_formatting_list = [f':{key}' for key in all_cols]
                str_formatting = ', '.join(str_formatting_list)
                insert_into_query = f'''
                            INSERT INTO {VENDUTO_TABLE_NAME} ({all_cols_str}) VALUES ({str_formatting});
                                        '''
                params = dict()
                for col in all_cols:
                    if col in data.keys():
                        params[col] = data.get(col)
                conn.execute(sqlalchemy.text(insert_into_query), params)
                conn.commit()
            conn.close()

    except Exception as e:
        print(f'Error in save_to_db_mysql() method:\n{e}')
        try:
            conn.close()
        except Exception as e:
            print(f'Error in save_to_db_mysql() method\n{e}')
    return None


if __name__ == '__main__':
    start = time.time()
    # define cities to scrape
    #region_city_list = [ ('Piemonte', 'Torino'), ('Lombardia', 'Milano'), ('Lazio', 'Roma')]
    parser = argparser.get_argparser()
    opts, _ = parser.parse_known_args()
    print('----SETTING UP VENDUTO SCRAPING PROCEDURE----')
    print('opts.scrapingCity')
    print(opts.scrapingCity)

    if opts.scrapingCity is None:
        print(f'Parameter Error: scrapingCity parameter must be not None and among {opts.valid_cities}\nFound: {opts.scrapingCity}')
    elif opts.scrapingCity not in opts.valid_cities:
        print(f'Parameter Error: scrapingCity parameter must be one among {opts.valid_cities}\nFound: {opts.scrapingCity}')
    else:
        city_scraped = opts.scrapingCity
        print('----SCRAPING PROCEDURE STARTED!----')
        print(f'----VENDUTO SCRAPING in {city_scraped}----')
        init_db_connection()
        # get links of agencies in city in immobiliare website
        agency_links = get_agency_links(city_scraped)
        # get venduto data from agencies url
        venduto_fetched = 0
        venduto_saved = 0
        for agency_link in agency_links:
            agency_ids = get_agency_venduto_data_id(agency_link)
            venduto_fetched += len(agency_ids)
            for siteId in agency_ids:
                comp = {'city': city_scraped,
                        'siteId': siteId,
                        'site': 'immobiliare',
                        'firstSeenDate': TODAY,
                        'lastSeenDate': TODAY,
                        'timesSeen': 1,
                        'sellingAgencyUrl': agency_link,
                        }
                save_to_db_mysql(comp)
                venduto_saved += 1
        print(f'{len(agency_links)} agency links fetched')
        print(f'{venduto_fetched} venduto ads where found')
        print(f'{venduto_saved} venduto ads were saved')
        print('----SCRAPING PROCEDURE ENDED!----')
    print('------------------------------------')
    print(f'Venduto scraping Procedure took {(time.time() - start)/60.} minutes to run')
