import argparse


def get_argparser():
    parser = argparse.ArgumentParser()

    # Dataset Options
    parser.add_argument("--ads_filename1", type=str, default='./data/Turin/annunci_mensili_202205.csv',
                        help="path to Dataset")
    parser.add_argument("--ads_filename2", type=str, default='./data/Torino/annunci_mensili_202206.csv',
                        help="path to Dataset")
    parser.add_argument("--house_info_filename", type=str, default='./data/Torino/HouseInfo.csv',
                        help="path to Dataset")
    parser.add_argument("--valid_cities", type=list, default=['Torino', 'Milano', 'Roma', 'Trieste'],
                        help="Cities model has been trained on")
    # ML model options
    parser.add_argument("--model_type", type=str, default='GAM',
                        help='ML model to use, can be one among "GAM" or "XGBR"')
    parser.add_argument("--training_city", type=str, default='Torino', help="City model has been trained on")
    # parser.add_argument("--valid", type=list, default=['marketZone',
    #                                                    'omiZone',
    #                                                    'latitude',
    #                                                    'longitude',
    #                                                    'maintenanceStatus',
    #                                                    'energyClass',
    #                                                    'constructionYear',
    #                                                    'grossSquareFootage',
    #                                                    'unitFloor',
    #                                                    'rooms',
    #                                                    'numberOfBathrooms',
    #                                                    'buildingFloorCount',
    #                                                    'elevator',
    #                                                    'terrace',
    #                                                    'balcony',
    #                                                    'gardenType',
    #                                                    'kitchenType',
    #                                                    'furnished',
    #                                                    'hasCantina',
    #                                                    'hasGarage',
    #                                                    'hasConcierge'],
    #                     help="Train columns from csv data")
    parser.add_argument("--valid", type=list, default=['marketZone',
                                                       'maintenanceStatus',
                                                       'unitFloor',
                                                       'elevator'],
                        help="Train columns from csv data")
    parser.add_argument("--target", type=str, default='pricePsm',
                        help="Ground truth column from csv data")

    # Scraping script
    parser.add_argument('--scrapingCity', type=str, default=None, help="City to scrape")

    return parser
