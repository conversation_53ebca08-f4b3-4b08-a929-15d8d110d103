import requests
from hashlib import sha256


data = dict()
tiresia_api_key = 'UlisseOcioATornareACasa'
data['api_key'] = sha256(tiresia_api_key.encode('utf-8')).hexdigest()
data['user_id'] = 'CQqR33ASjciob9J6u1Lq'
data['user_type'] = 'agency'
data['project_id'] = 'ChxoDwQ8eWRMKR5CfMgr'
data['generation_type'] = "nakedInteriors" #nakedInteriors, furnishedOriginal, furnishedRenewed
# data['generation_style'] = "stockholm" #rome, stockholm, paris, new-york, tokyo, avignone


print(data)
res = requests.post('http://127.0.0.1:5000/immaginaSmart', params=data)
# res = requests.post('https://newarc-api-staging.herokuapp.com/immaginaSmart', params=data)
# res = requests.post('https://newark-api.herokuapp.com/immaginaSmart', params=data)

print(res.url)
print(res.status_code)
print(res.json())
print(res.json()["status"])