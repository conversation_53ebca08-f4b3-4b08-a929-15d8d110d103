def test_post_queryContacts(client):
    rv = client.post("/queryContacts")
    print(rv.json)
    assert rv.status_code == 200
    assert rv.json == {'status': 401, 'error': 'api_key is required'}


def test_get_queryContacts(client):
    rv = client.get("/queryContacts")
    print(rv.json)
    assert rv.status_code == 405
    assert rv.json is None


# def test_post_queryContacts_city(client):
#     city = 'Firenze'
#     url = f"/queryContacts?city={city}"
#     rv = client.post(url)
#     assert rv.status_code == 200
#     assert rv.json == {'error': "'NoneType' object has no attribute 'execute'", 'status': 500}