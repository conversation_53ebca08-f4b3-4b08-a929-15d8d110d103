from bs4 import BeautifulSoup
import numpy as np
import pandas as pd
import requests
import time
import re
import os
import random
from geopy.geocoders import Nominatim
from datetime import datetime, timedelta, date
import csv
import argparser
import utils
import sqlalchemy
import math
from threading import Thread
import pickle
import copy
import json
import sys
import ssl

from consts import (DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, DB_TABLE_NAME,
                    TODAY, IMMOBILIARE_SCRAPING_DICT, SCRAPER_SQL_GOOGLE_CREDENTIALS, DISCR_COLS,
                    PROXY_HOST, PROXY_PORT, PROXY_USER, PROXY_PASSWORD)
import warnings
warnings.filterwarnings("ignore")

scraped_comparabili = 0
duplicate_comparabili = 0
new_comparabili = 0

# re utils
HTML_CLEANR = re.compile('<.*?>')
NUM_CLEANER = re.compile('[^0-9]')
ELEVATOR_BOOL = re.compile('.*con ascensore.*')

# CMA params
TIME_DELTA = timedelta(days=180)
DISTANCE_DELTA = 1

scraping_db = None
def init_db_connection() -> None:
    """Initiates connection to database and its' structure."""
    global scraping_db
    if scraping_db is None:
        scraping_db = utils.connect_with_connector(DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, SCRAPER_SQL_GOOGLE_CREDENTIALS)


def time_it(func):
    '''
    Decorator that prints to console the Elapsed time for a function to which is applied
    '''
    def inner(*args, **kwargs):
        start_time = time.time()
        print(f'{func.__name__} is starting!')
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = (end_time - start_time)/60.
        print('{0} took {1:.8f}min to execute'.format(func.__name__, execution_time))
        return result
    return inner

@time_it
def random_sleep():
    rand_sleep = random.randint(10, 30)
    sleep_time = rand_sleep/10
    time.sleep(sleep_time)


def clean_html_string(text:str) -> str:
    cleantext = re.sub(HTML_CLEANR, ',', text)
    cleantext = re.sub(' +', ' ', cleantext)
    cleantext = re.sub(',+', ',', cleantext)
    cleantext = cleantext.strip(',')
    return cleantext


def remove_nonnumbers_from_string(text:str) -> str:
    cleantext = re.sub(NUM_CLEANER, '', text)
    cleantext = cleantext.strip()
    return cleantext


def write_csv(data: list, file:str):
    with open(file, 'a', newline='', encoding='utf-8') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(data)


def extract_last_streetNum_from_string_if_so(string: str):
    match = re.search(r'\d+[\\\/]*\d*\w*.?$', string)
    number = None
    if match:
        number = match.group()
        number = number.replace(".", "")
    return number


def clean_streetName_from_numbers(string: str):
    match = re.search(r'([nN]°)?\d+[\\\/]*\d*\w*.?$', string)
    if match:
        string = string.replace(match.group(), "")
        string = string.replace(",", "")
        string = string.replace(".", "")
    return string.strip()


def get_streetNum_from_streetName(string: str):
    match = re.search(r',?\d+[\\\/]*\d*\w*.?$', string)
    number = None
    if match:
        number = match.group()

    return number


def extract_zipcode(string: str):
    match = re.search(r'[0-9]{5}', string)
    zipcode = None
    if match:
        zipcode = int(match.group())
    return zipcode


def geocode(address, user_agent, attempt=1, max_attempts=2):
    try:
        return Nominatim(user_agent=user_agent, timeout=3).geocode(address)
    except Exception:
        if attempt <= max_attempts:
            print('in exception, attempt number: ', attempt)
            time.sleep(5)
            return geocode(address, user_agent, attempt=attempt+1)
        raise


def get_conto_economico(comp_dict:dict, selling_market_price_col: str) -> tuple:
    # fixed ROI at 20%
    buy_price, min_discount, selling_price, fixed_cost, variable_cost, utile, costs = None, None, None, None, None, None, None
    if comp_dict['grossSquareFootage'] is not None and comp_dict[selling_market_price_col] is not None and int(comp_dict[
        'grossSquareFootage']) > 13:
        notaio = 1500
        agency_sell = 4000 * 1.22
        renewal_costPsm = 500  # euroPsm
        utile_fixed_percentage = .16
        fixed_cost = notaio + agency_sell
        renewal_cost = renewal_costPsm * int(comp_dict['grossSquareFootage'])
        selling_price = comp_dict[selling_market_price_col] * int(comp_dict['grossSquareFootage'])
        if comp_dict['maintenanceStatus'] not in ['Nuovo / In costruzione']:
            fixed_cost += renewal_cost
        # keep in mind factor .13 in formula below is summation of taxes and agency_buy coefficients
        buy_price = (selling_price - fixed_cost - (utile_fixed_percentage*fixed_cost)) / (1 + .13 + (.13 * utile_fixed_percentage) + utile_fixed_percentage)
        taxes = 0.09 * buy_price
        agency_buy = 0.04 * buy_price
        variable_cost = agency_buy + taxes
        costs = fixed_cost + variable_cost
        utile = utile_fixed_percentage * (costs + buy_price)
        if comp_dict['price'] is not None and int(comp_dict['price']) != 0:
            min_discount = (buy_price - int(comp_dict['price'])) / (int(comp_dict['price']) + 1.e-2)
    return buy_price, min_discount, selling_price, costs, utile

class Response:
    def __init__(self, content, status_code):
        self.content = content
        self.status_code = status_code
        self.text = content.decode('utf-8')

asked_requests = 0
answered_requests = 0
def handle_request(url, max_retries=5):
    global asked_requests
    global answered_requests
    asked_requests += 1
    ssl._create_default_https_context = ssl._create_unverified_context
    if sys.version_info[0] == 3:
        import urllib.request
        opener = urllib.request.build_opener(
            urllib.request.ProxyHandler(
                {'http': f'http://{PROXY_USER}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}',
                 'https': f'http://{PROXY_USER}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}'}))
        for i in range(max_retries):
            try:
                response = opener.open(url)
                # ip_used = opener.open('https://api.ipify.org').read().decode('utf8')
                # print(f"IP used for request: {ip_used}")
                answered_requests += 1
                return Response(response.read(), response.getcode())
            except (urllib.error.HTTPError, urllib.error.URLError) as e:
                code = getattr(e, 'code', "nocode")
                reason = getattr(e, 'reason', "noreason")
                print(f"handle_request urllib.error: {code} {reason}\n{e}")
                if code in [200, 404]:
                    answered_requests += 1
                    return Response(e.read(), e.code)
                else:
                    random_sleep()
                    continue
        print(f"handle_request failed after {max_retries} retries")
        return Response("", 0)

@time_it
def get_immobiliare_marketZones(region: str, city: str) -> list:
    '''
    Given region, city, scrapes from immobiliare.it data on marketZones of the city as told by immobiliare.it.
    Needed to encode url for request.

    Parameters
    ----------
    region: str
        region where city lies in
    city: str
        city to scrape
    
    Returns
    -------
    scraped_list: list
        list of marketZones encoded as immobiliare wants for reaching its endpoints
    '''
    
    region = region.lower()
    city = city.lower()

    print('get_immobiliare_marketZones', 'was called on', city.upper(), region.upper())

    scraped_list = list()
    url = f'https://www.immobiliare.it/mercato-immobiliare/{region}/{city}/'
    try:
        # content = requests.get(url)
        content = handle_request(url)
        soup = BeautifulSoup(content.text, "html.parser")
        zoneTag = soup.find_all("tr", {'class': "nd-table__row"})
        if zoneTag is not None:
            for tag in zoneTag:
                marketZone = tag.find('a', {'class': 'nd-table__url'}, href=True)['href'].strip('/').split('/')[-1]
                scraped_list.append(marketZone)

    except Exception as e:
        print('get_immobiliare_marketZones', ' failed')
        print('Error: ', e)

    return scraped_list


@time_it
def scrape_immobiliare_ads_links_in_city_marketZone(city: str, marketZone: str) -> list[dict]:
    """
    Scrapes links of first up to 900000 ads on immobiliare.it for a certain city and marketZone.  
    Limit is very high, usually far fewer links are available. Discretizing by marketZone is needed 
    because of limits to data shown on immobiliare.it

    Parameters
    ----------
    city: str
        city name in italian for the city we want to scrape
    marketZone: str
        one of the marketZones of the city as told by immobiliare.it
      
    Returns
    -------
    links: list[dict]
        list of first up to 900000 [{'url': url, 'price': price}] to ads on immobiliare for the city 
        and marketZone selected
    """
    city = city.lower()
    links = []
    count = 0
    page_number = 0
    print(">Fetching links")
    while (len(links)<900000):
        page_number += 1
        # code to be used for faster computational time (only one page is fetched)
        # if page_number == 2:
        #     print(">>Scraping url finished")
        #     break
        url = f'https://www.immobiliare.it/vendita-case/{city}/{marketZone}/?criterio=rilevanza&pag={page_number}&noAste=1&tipoProprieta=1'
        try:
            # content = requests.get(url)
            content = handle_request(url)
            print(">>Page Number: " + str(page_number) + ' >>Response: ' + str(content.status_code))
            
            soup = BeautifulSoup(content.text, "html.parser")
            magic_script = soup.find('script', id="__NEXT_DATA__")
            if not magic_script:
                print(f"No magic script found for URL: {url}")
                print(">>Scraping url finished")
                break
            data_dict = json.loads(magic_script.text)
            # with open(f"data_dict_{page_number}.txt", "w", encoding="utf-8") as file:
            #     json.dump(data_dict, file, ensure_ascii=False, indent=4)
            if not 'dehydratedState' in data_dict['props']['pageProps']:
                print(f"No 'dehydratedState' found in magic script for URL: {url}")
                print(">>Scraping url finished")
                break
            divTag = soup.find_all("div", {'class': "nd-mediaObject__content"})
            if len(divTag) == 0:
                print(f"No data found in html for URL: {url}")
                print(">>Scraping url finished")
                break
            page_results = data_dict['props']['pageProps']['dehydratedState']['queries'][0]['state']['data']['results']
            for res in page_results:
                _price = None
                if 'value' in res['realEstate']['price']:
                    _price  = res['realEstate']['price']['value']
                comp_dict = {
                    'url': res['seo']['url'], 
                    'price': _price,
                }
                links.append(comp_dict)
                count += 1
            # divTag = soup.find_all("div", {'class': "nd-mediaObject__content"})
            # if len(divTag) == 0:
            #     print(">>Scraping url finished")
            #     break
            # else:
            #     for tag in divTag:
            #         tdTags = tag.find_all("a")
            #         for tag in tdTags:
            #             l = tag['href']
            #             links.append(l)
            #             count += 1
        except Exception as e:
            print(f'Error in scrape_immobiliare_ads_links_in_city_marketZone\n url:{url}\n error:{e}')
        if count % 250 == 0:
            print(count, 'links fetched and counting...')
    return links

@time_it
def get_new_and_price_updated_links_update_others(links_prices: list, city: str):
    """
    Given a list of links to immobiliare ads, checks on db which are new, which have updated price, which have same price and returns them.

    Parameters
    ----------
    links_prices: list[dict]
        list[dict] of {'url':link, 'price':price} of immobiliare ads fetched
    
    Returns
    -------
    new_links: list[str]
        list of new links
    updated_price_links: list[str]
        list of links with updated price
    same_price_links: list[str]
        list of links with same price
    """
    new_links = []
    same_price_links = []
    updated_price_links = []
    if len(links_prices) == 0:
        return new_links, updated_price_links, same_price_links
    with scraping_db.connect() as conn:
        # use db
        query = f'''USE {DB_NAME};'''
        query = sqlalchemy.text(query)
        conn.execute(query)
        conn.commit()
        # select duplicate links and prices
        is_link_duplicate_query = f"SELECT link, price, timesSeen FROM {DB_TABLE_NAME} WHERE link IN ({','.join([f'"{link["url"]}"' for link in links_prices])}) ORDER BY firstPriceDate desc;"
        is_link_duplicate_query = sqlalchemy.text(is_link_duplicate_query)
        comp_duplicates = conn.execute(is_link_duplicate_query).fetchall()
        comp_duplicates = pd.DataFrame(comp_duplicates).to_dict(orient="records")
        # iterate on fetched links and prices
        for fetched_link_price in links_prices:
            try:
                # check if link is already in db
                if fetched_link_price['url'] in [comp['link'] for comp in comp_duplicates]:
                    old_comp = [comp for comp in comp_duplicates if comp['link'] == fetched_link_price['url']][0]
                    # check if price is the same 
                    if math.isnan(old_comp['price']):
                        old_comp['price'] = None
                    if ((old_comp['price'] is not None) and (fetched_link_price['price'] is not None)) | (old_comp['price'] == fetched_link_price['price']):
                        old_comp['timesSeen'] += 1
                        update_query = f'''UPDATE {DB_TABLE_NAME} SET lastSeenDate = :lastSeenDate, timesSeen = :timesSeen WHERE link = :url;'''
                        params = {
                            'lastSeenDate': TODAY,
                            'timesSeen': old_comp['timesSeen'],
                            'url': fetched_link_price['url'],
                        }
                        update_query = sqlalchemy.text(update_query)
                        conn.execute(update_query, parameters=params)
                        conn.commit()
                        same_price_links.append(fetched_link_price['url'])
                    else:
                        updated_price_links.append(fetched_link_price['url'])                        
                else:
                    new_links.append(fetched_link_price['url'])
            except Exception as e:
                print(f'Error in get_new_and_price_updated_links_update_others:\nurl: {fetched_link_price['url']}\nerror: {e}\n')
        conn.close()
    return new_links, updated_price_links, same_price_links


@time_it
def scrape_immobiliare_ads(links: list, thread_id: int, opts) -> None:
    """
    Scrapes immobiliare ads given as a list of urls looking for interesting information.
    Then saves to db.
    See code to find out what data is being scraped.
      
    Parameters
    ----------
    links: list[str]
        list of immobiliare ads to be scraped
    thread_id: int
        id of thread that called this function
    opts: argparse.Namespace
        options passed to the script
    """
    i = 0
    for url in links:
        try:
            ad_scraping_start_time = time.time()
            comparabileClass_list = ['Classe immobile economica',
                                     'Classe immobile media',
                                     'Classe immobile signorile',
                                     'Immobile di lusso']
            # get data
            # content = requests.get(url)
            content = handle_request(url)
            soup = BeautifulSoup(content.text, "html.parser")
            magic_script = soup.find('script', id="__NEXT_DATA__")
            if not magic_script:
                print(f"No data found for URL: {url}")
                continue
            data_dict = json.loads(magic_script.text)
            data_dict = data_dict['props']['pageProps']['detailData']['realEstate']
            # scrape data
            siteId = url.rstrip('/').split('/')[-1]
            title = None
            if 'title' in data_dict.keys():
                title = data_dict['title']
            sellingAgency, sellingAgencyUrl = None, None
            if 'advertiser' in data_dict.keys():
                if 'agency' in data_dict['advertiser'].keys():
                    if 'displayName' in data_dict['advertiser']['agency'].keys():
                        sellingAgency = data_dict['advertiser']['agency']['displayName']
                    if 'agencyUrl' in data_dict['advertiser']['agency'].keys():
                        sellingAgencyUrl = data_dict['advertiser']['agency']['agencyUrl']
                elif 'supervisor' in data_dict['advertiser'].keys():
                    if 'type' in data_dict['advertiser']['supervisor'].keys():
                        if data_dict['advertiser']['supervisor']['type'] == 'user':
                            sellingAgency = 'privato'
                            if 'phones' in data_dict['advertiser']['supervisor'].keys():
                                if len(data_dict['advertiser']['supervisor']['phones']) > 0:
                                    if data_dict['advertiser']['supervisor']['phones'][0] is dict:
                                        if 'value' in data_dict['advertiser']['supervisor']['phones'][0].keys():
                                            sellingAgencyUrl = data_dict['advertiser']['supervisor']['phones'][0]['value']
            adInsertionDate = None
            if 'createdAt' in data_dict.keys():
                adInsertionDate = datetime.fromtimestamp(data_dict['createdAt'])
            price = None
            if 'price' in data_dict.keys():
                if 'value' in data_dict['price'].keys():
                    price = data_dict['price']['value']
            maintenanceStatus, constructionYear, energyClass = None, None, None
            balcony, terrace, zipCode = False, False, None
            gardenType, furnished = 'nessuno', 'non arredato'
            externalFrames, hasCantina, externalEsposition, hasConcierge = None, False, None, False
            heatingType, heatingEmitter, heatingFuel = None, None, None
            elevator, numberOfBathrooms, description = False, None, None
            hasGarage, numCarPlaces, airConditioning = False, 0, False
            unitFloor, buildingFloorCount, latitude, longitude = None, None, None, None
            streetName, streetNum, marketZone, city, rooms = None, None, None, None, None
            grossSquareFootage, propertyType, comparabileClass, kitchenType = None, None, None, None
            images_dict = dict()
            planimetry_dict = dict()
            if 'properties' in data_dict.keys():
                if len(data_dict['properties']) > 0:
                    temp_dict = data_dict['properties'][0]
                    if 'condition' in temp_dict.keys():
                        maintenanceStatus = temp_dict['condition']
                    if 'buildingYear' in temp_dict.keys():
                        constructionYear = temp_dict['buildingYear']
                    if 'energy' in temp_dict.keys():
                        if 'class' in temp_dict['energy'].keys():
                            energyClass = temp_dict['energy']['class']
                            if isinstance(energyClass, dict):
                                if 'name' in temp_dict['energy']['class'].keys():
                                    energyClass = temp_dict['energy']['class']['name']
                        if 'heatingType' in temp_dict['energy'].keys():
                            heating_list = temp_dict['energy']['heatingType'].split(',')
                            if len(heating_list) == 3:
                                heatingType = heating_list[0].strip()
                                heatingEmitter = heating_list[1].strip()
                                heatingFuel = heating_list[2].strip()
                            else:
                                heatingType = heating_list[0].strip()
                        if 'airConditioning' in temp_dict['energy'].keys():
                            airConditioning = True
                    if 'surfaceConstitution' in temp_dict.keys():
                        if 'surfaceConstitutionElements' in temp_dict['surfaceConstitution']:
                            if type(temp_dict['surfaceConstitution']['surfaceConstitutionElements']) is list:
                                unitFloorsList = list()
                                for element in temp_dict['surfaceConstitution']['surfaceConstitutionElements']:
                                    if 'constitution' in element.keys():
                                        if element['constitution'].lower() == 'abitazione':
                                            if 'floor' in element.keys():
                                                unitFloorsList.append(element['floor']['value'])
                                if len(unitFloorsList)>1:
                                    multipleUnitFloorsList = list(set(unitFloorsList))
                                    if len(multipleUnitFloorsList)>1:
                                        multipleUnitFloorsList.sort(key=utils.unitFloor_sorting_key)
                                        unitFloor = multipleUnitFloorsList[0]
                                    else:
                                        unitFloor = multipleUnitFloorsList[0]
                                elif len(unitFloorsList)==1:
                                    unitFloor = unitFloorsList[0]
                    elif 'floor' in temp_dict.keys():
                        if 'ga4FloorValue' in temp_dict['floor'].keys():
                            if temp_dict['floor']['ga4FloorValue'] is not None:
                                unitFloor = temp_dict['floor']['ga4FloorValue']
                            elif 'value' in temp_dict['floor'].keys():
                                if re.match(r'.*piani.*', temp_dict['floor']['value']):
                                    unitFloor = 'Multipiano'
                    if 'garage' in temp_dict.keys():
                        hasGarage = True
                        garage_list = temp_dict['garage'].split(',')
                        if garage_list is list:
                            numCarPlaces = sum([float(remove_nonnumbers_from_string(gar)) for gar in garage_list])
                        elif garage_list is str:
                            numCarPlaces = float(remove_nonnumbers_from_string(garage_list))
                    if 'floors' in temp_dict.keys():
                        buildingFloorCount = remove_nonnumbers_from_string(temp_dict['floors'])
                    if 'elevator' in temp_dict.keys():
                        elevator = temp_dict['elevator']
                    if 'bathrooms' in temp_dict.keys():
                        numberOfBathrooms = remove_nonnumbers_from_string(temp_dict['bathrooms'])
                    if 'description' in temp_dict.keys():
                        description = temp_dict['description']
                    if 'location' in temp_dict.keys():
                        if 'latitude' in temp_dict['location'].keys():
                            if temp_dict['location']['latitude'] and temp_dict['location']['latitude'] != "":
                                latitude = temp_dict['location']['latitude']
                        if 'longitude' in temp_dict['location'].keys():
                            if temp_dict['location']['longitude'] and temp_dict['location']['longitude'] != "":
                                longitude = temp_dict['location']['longitude']
                        if 'city' in temp_dict['location'].keys():
                            city = temp_dict['location']['city']
                        if 'address' in temp_dict['location'].keys():
                            if temp_dict['location']['address'] and temp_dict['location']['address'] != '':
                                streetName = clean_streetName_from_numbers(temp_dict['location']['address'])
                        if 'streetNumber' in temp_dict['location'].keys():
                            streetNum = temp_dict['location']['streetNumber']
                        if streetNum is None and streetName is not None:
                            streetNum = get_streetNum_from_streetName(streetName)
                        if streetNum is None:
                            streetNum = extract_last_streetNum_from_string_if_so(title.split(',')[0])
                        if streetNum is None:
                            streetNum = extract_last_streetNum_from_string_if_so(title.split(',')[1])
                        if 'microzone' in temp_dict['location'].keys():
                            if temp_dict['location']['microzone'] and temp_dict['location']['microzone'] != '':
                                marketZone = temp_dict['location']['microzone'].lower()
                    if 'multimedia' in temp_dict.keys():
                        if 'photos' in temp_dict['multimedia'].keys():
                            images_dict = str({obj['urls']['medium']: obj['caption'] for obj in temp_dict['multimedia']['photos'] if (obj['urls'] is not None)})
                        if 'floorplans' in temp_dict['multimedia'].keys():
                            planimetry_dict = str({obj['urls']['medium']: obj['caption'] for obj in temp_dict['multimedia']['floorplans'] if (obj['urls'] is not None)})
                    if 'rooms' in temp_dict.keys():
                        rooms = remove_nonnumbers_from_string(temp_dict['rooms'])
                    if 'kitchenStatus' in temp_dict.keys():
                        kitchenType = temp_dict['kitchenStatus']
                    if 'surface' in temp_dict.keys():
                        grossSquareFootage = remove_nonnumbers_from_string(temp_dict['surface'])
                    if 'typology' in temp_dict.keys():
                        if 'name' in temp_dict['typology'].keys():
                            propertyType = temp_dict['typology']['name']
                    if 'typologyValue' in temp_dict.keys():
                        types_list = temp_dict['typologyValue'].split('|')
                        for typ in types_list:
                            if re.match('.*immobile.*', typ.lower()):
                                comparabileClass = typ.strip()
                                if comparabileClass not in comparabileClass_list:
                                    comparabileClass = None
                    if 'features' in temp_dict.keys():
                        for feature in temp_dict['ga4features']:
                            if re.match('.*balcon[ei].*', feature):
                                balcony = True
                            if re.match('.*terrazz[ea].*', feature):
                                terrace = True
                            if re.match('.*giardin[oi].*', feature):
                                gardenType = feature
                            if re.match('.*arredat[oaie].*', feature):
                                furnished = feature
                            if re.match('.*infiss[io].*', feature):
                                externalFrames = re.sub('infiss[io] estern[io]', '', feature).strip()
                            if re.match('.*cantina.*', feature):
                                hasCantina = True
                            if re.match('.*esposizione.*', feature):
                                externalEsposition = re.sub('esposizione', '', feature)
                                externalEsposition = externalEsposition.strip()
                            if re.match('.*portiere.*', feature):
                                hasConcierge = True
            # parse pricePsm
            pricePsm = None
            if grossSquareFootage is not None and price is not None:
                if float(grossSquareFootage) != 0 and float(price) != 0:
                    pricePsm = str(int(float(price)/float(grossSquareFootage)))
            # compute omiZone
            omiZone_obj = utils.OmiZone(f"data/{city}/{city}_OmiZones_2022.kml")
            omiZone = None
            if latitude is not None and longitude is not None:
                omiZone = omiZone_obj.getOmiZone(longitude, latitude)
            # update results dict with scraped info
            scraped_dict = {'site': 'immobiliare',
                            'siteId': siteId,
                            'link': url,
                            'title': title,
                            'streetName': streetName,
                            'streetNum': streetNum,
                            'city': city,
                            'marketZone': marketZone,
                            'omiZone': omiZone,
                            'latitude': latitude,
                            'longitude': longitude,
                            'zipCode': zipCode,
                            'description': description,
                            'propertyType': propertyType,
                            'comparabileClass': comparabileClass,
                            'constructionYear': constructionYear,
                            'grossSquareFootage': grossSquareFootage,
                            'rooms': rooms,
                            'numberOfBathrooms': numberOfBathrooms,
                            'kitchenType': kitchenType,
                            'unitFloor': unitFloor,
                            'buildingFloorCount': buildingFloorCount,
                            'elevator': elevator,
                            'maintenanceStatus': maintenanceStatus,
                            'energyClass': energyClass,
                            'balcony': balcony,
                            'terrace': terrace,
                            'gardenType': gardenType,
                            'furnished': furnished,
                            'externalFrames': externalFrames,
                            'externalEsposition': externalEsposition,
                            'airConditioning': airConditioning,
                            'heatingType': heatingType,
                            'heatingEmitter': heatingEmitter,
                            'heatingFuel': heatingFuel,
                            'hasCantina': hasCantina,
                            'hasConcierge': hasConcierge,
                            'hasGarage': hasGarage,
                            'numCarPlaces': numCarPlaces,
                            'price': price,
                            'pricePsm': pricePsm,
                            'imagesUrls': images_dict,
                            'planimetry': planimetry_dict,
                            'adInsertionDate': adInsertionDate,
                            'firstPrice': price,
                            'firstPriceDate': TODAY,
                            'lastPriceDate': TODAY,
                            'lastSeenDate': TODAY,
                            'timesSeen': 1,
                            'sellingAgency': sellingAgency,
                            'sellingAgencyUrl': sellingAgencyUrl,
                            'marketPricePsm': None,
                            'marketPriceDelta': None,
                            'newarcScore': None,
                            'renewedMarketPricePsm': None,
                            'grossMargin': None,
                            'offerPrice': None,
                            'minDiscount': None,
                            'sellingPrice': None,
                            'costs': None,
                            'predPricePsm': None,
                            'predPriceDelta': None,
                            'predRenewedPricePsm': None,
                            'predNewarcRenewedPricePsm': None,
                            'predOfferPrice': None,
                            'predMinDiscount': None,
                            'predSellingPrice': None,
                            'predCosts': None,
                            'predGrossMargin': None,
                            'microzonaScore': 69,
                            'stabileScore': 69,
                            'immobileScore': 69,
                            'serviziScore': 69,
                            'velocitàScore': 69
            }
            scraped_dict = get_model_preds_on_scraped_data(scraped_dict, opts)
            # get conto economico on predicted values
            (scraped_dict['predOfferPrice'], scraped_dict['predMinDiscount'], scraped_dict['predSellingPrice'],
             scraped_dict['predCosts'], scraped_dict['predGrossMargin']) = \
                (get_conto_economico(scraped_dict, 'predNewarcRenewedPricePsm'))
            # save ads
            dupl, new = save_to_db_mysql(scraped_dict)
            i += 1
            if i%100 == 0:
                print(i, '-esimo link processato')
            global new_comparabili
            new_comparabili += new
            global duplicate_comparabili
            duplicate_comparabili += dupl
            global scraped_comparabili
            scraped_comparabili += 1
            # lower city for better filename format
            #city = city.lower()

            # WRITING TO CSV FILE row by row
            # # whenever file doesn't exist, write column names first
            # if f'{city}_immobiliare_scraped_data_{today}.csv' not in os.listdir():
            #     write_csv(list(scraped_dict[0].keys()), f'{city}_immobiliare_scraped_data_{today}.csv')
            # # save row to .csv file
            # write_csv(scraped_dict[0].values(), f'{city}_immobiliare_scraped_data_{today}.csv')
            ad_scraping_tot_time = time.time() - ad_scraping_start_time
            if 1-ad_scraping_tot_time > 0:
                time.sleep(1-ad_scraping_tot_time)
        except Exception as e:
            try:
                print(f'Error in scrape_immobiliare_ads:\nsiteId: {siteId}\nerror: {e}')
            except:
                print(f'Error in scrape_immobiliare_ads:\nsiteId: siteIdException\nerror: {e}')
            finally:
                time.sleep(5)
    print('out of', len(links), ' links fetched ', i, ' were scraped')


def get_model_preds_on_scraped_data(data_scraped: dict, opts) -> dict:
    """
    Get model predicitons on scraped data after preprocessing and encoding

    Parameters
    ----------
    data_scraped : dict
        Dictionary containing scraped data
    opts : argparse.Namespace
        Command line arguments
    """
    try:
        scraped_data = pd.DataFrame.from_dict([data_scraped])
        if int(scraped_data[opts.valid].isna().sum(axis=1).iloc[0]) == 0:
            # compute ML model price features
            opts.training_city = opts.scrapingCity
            # load ml model
            model_file_path = f'./model/{opts.training_city}/'
            if not os.path.exists(model_file_path):
                raise FileNotFoundError(f"Model file path {model_file_path} does not exist.")
            model = None
            if opts.model_type == 'GAM':
                for file in os.listdir(model_file_path):
                    if re.match('.*GAM.*', file):
                        model_file_path += file
                model = pickle.load((open(model_file_path, 'rb')))
            # preprocess data
            # unitFloor
            if re.match(".*[Ss]eminterrato.*", scraped_data.at[0, 'unitFloor']):
                scraped_data.at[0, 'unitFloor'] = "-0.5"
            elif re.match(".*[Tt]erra.*", scraped_data.at[0, 'unitFloor']):
                scraped_data.at[0, 'unitFloor'] = "0.0"
            elif re.match(".*[Rr]ialzato.*", scraped_data.at[0, 'unitFloor']):
                scraped_data.at[0, 'unitFloor'] = "0.25"
            elif re.match(".*[Aa]mmezzato.*", scraped_data.at[0, 'unitFloor']):
                scraped_data.at[0, 'unitFloor'] = "0.5"
            elif re.match('Interrato.*', scraped_data.at[0, 'unitFloor']):
                scraped_data.at[0, 'unitFloor'] = scraped_data.at[0, 'unitFloor'].replace('Interrato ',
                                                                                          '').replace(')',
                                                                                                      '').replace('(', '')
            elif re.match('.*[Uu]ltimo.*', scraped_data.at[0, 'unitFloor']):
                    scraped_data.at[0, 'unitFloor'] = scraped_data.at[0, 'buildingFloorCount']
            scraped_data['unitFloor'] = scraped_data['unitFloor'].apply(pd.to_numeric, downcast='float', errors='coerce')
            scraped_data['unitFloor'] = scraped_data['unitFloor'].apply(utils.my_unitFloor)
            scraped_data = scraped_data[opts.valid]
            if int(scraped_data.isna().sum(axis=1).iloc[0]) == 0:
                # encode data
                ottim_ristr_data = copy.deepcopy(scraped_data)
                nuovo_costr_data = copy.deepcopy(scraped_data)
                ottim_ristr_data.at[0, 'maintenanceStatus'] = 'Ottimo / Ristrutturato'
                nuovo_costr_data.at[0, 'maintenanceStatus'] = 'Nuovo / In costruzione'
                scraped_data = utils.encoding(scraped_data, opts)
                ottim_ristr_data = utils.encoding(ottim_ristr_data, opts)
                nuovo_costr_data = utils.encoding(nuovo_costr_data, opts)
                # predict prices
                result = float(model.predict(scraped_data)[0])
                ottim_ristr_pred = float(model.predict(ottim_ristr_data)[0])
                nuovo_costr_pred = float(model.predict(nuovo_costr_data)[0])
                # correct prices
                result -= utils.compute_price_correction(data_scraped['city'],
                                                         data_scraped['marketZone'],
                                                         data_scraped['maintenanceStatus'],
                                                         data_scraped['elevator'])
                ottim_ristr_pred -= utils.compute_price_correction(data_scraped['city'],
                                                                   data_scraped['marketZone'],
                                                                   'Ottimo / Ristrutturato',
                                                                   data_scraped['elevator'])
                nuovo_costr_pred -= utils.compute_price_correction(data_scraped['city'],
                                                                   data_scraped['marketZone'],
                                                                   'Ottimo / Ristrutturato',
                                                                   data_scraped['elevator'])
                # compute ristrutturato a nuovo price
                ristrutturato_a_nuovo_pred = ottim_ristr_pred + abs(nuovo_costr_pred - ottim_ristr_pred) * 0.3
                # assign pred values to scraped dict
                data_scraped['predPricePsm'] = result
                data_scraped['predRenewedPricePsm'] = ottim_ristr_pred
                data_scraped['predNewarcRenewedPricePsm'] = ristrutturato_a_nuovo_pred
                if data_scraped['pricePsm'] is not None and result is not None:
                    data_scraped['predPriceDelta'] = (int(data_scraped['pricePsm']) - result) / (result + 1.e-2)
    except Exception as e:
        print(f'Error in get_model_preds_on_scraped_data:\n{e}')
    return data_scraped


def create_db_table_mysql():
    with scraping_db.connect() as conn:
        # creating db if it doesn't exist
        query = f'''CREATE DATABASE IF NOT EXISTS {DB_NAME};'''
        query = sqlalchemy.text(query)
        conn.execute(query)
        conn.commit()
        # use db
        query = f'''USE {DB_NAME};'''
        query = sqlalchemy.text(query)
        conn.execute(query)
        conn.commit()
        # create table if it doesn't exist
        table_def_string = ''
        for key in IMMOBILIARE_SCRAPING_DICT.keys():
            table_def_string += key + ' ' + IMMOBILIARE_SCRAPING_DICT[key] + ','
        table_def_string = table_def_string.strip(',')
        create_table_query = f'''CREATE TABLE IF NOT EXISTS {DB_TABLE_NAME} ({table_def_string});'''
        create_table_query = sqlalchemy.text(create_table_query)
        conn.execute(create_table_query)
        conn.commit()
        conn.close()


def save_to_db_mysql(comp: dict) -> tuple:
    '''
    Saves comparabile data to mysql DB. Data must be given as a dictionary with keys equal as mysql DB column names.
    only takes one row at a time.

    Parameters
    ----------
    comp: dict
        dictionary with comparabile's data

    Returns
    -------
    tup: tuple
        tuple (is_it_duplicate, is_it_new)
    '''
    with scraping_db.connect() as conn:
        # use db
        query = f'''USE {DB_NAME};'''
        query = sqlalchemy.text(query)
        conn.execute(query)
        conn.commit()
        # check whether input comp is duplicate and take action accordingly
        is_it_duplicate = 0
        is_it_new = 0
        # check whether comp is a duplicate according to LINK
        is_link_duplicate_query = f'''SELECT * FROM {DB_TABLE_NAME} WHERE link = :link;'''
        is_link_duplicate_query = sqlalchemy.text(is_link_duplicate_query)
        link_duplicates = conn.execute(is_link_duplicate_query, parameters={"link": comp.get('link')}).fetchall()
        link_duplicates = pd.DataFrame(link_duplicates).to_dict(orient="records")
        if len(link_duplicates) != 0:
            if len(link_duplicates) > 1:
                print('Theres more than one duplicate. ')
                print('# duplicates that share link: ', len(link_duplicates))
            new_comp = comp
            old_comp = link_duplicates[0]
            # update values
            old_comp['lastSeenDate'] = TODAY
            old_comp['timesSeen'] += 1
            if new_comp['price'] is not None:
                if old_comp['price'] is None:
                    old_comp['price'] = 0
                if not old_comp['price']*0.95 < float(new_comp['price']) < old_comp['price']*1.05:
                    old_comp['price'] = float(new_comp['price'])
                    old_comp['lastPriceDate'] = TODAY
                    old_comp['pricePsm'] = old_comp['price']/float(old_comp['grossSquareFootage'])

            old_comp['latitude'] = new_comp['latitude']
            old_comp['longitude'] = new_comp['longitude']
            old_comp['planimetry'] = new_comp['planimetry']
            old_comp['maintenanceStatus'] = new_comp['maintenanceStatus']
            old_comp['omiZone'] = new_comp['omiZone']
            old_comp['unitFloor'] = new_comp['unitFloor']
            old_comp['energyClass'] = new_comp['energyClass']
            for col in ['predPricePsm', 'predPriceDelta', 'predRenewedPricePsm', 'predNewarcRenewedPricePsm',
                        'predOfferPrice', 'predMinDiscount', 'predSellingPrice', 'predCosts', 'predGrossMargin']:
                old_comp[col] = new_comp[col]
            # update old row with new information
            update_query = f'''
            UPDATE {DB_TABLE_NAME} 
            SET lastSeenDate = :lastSeenDate,
                timesSeen = :timesSeen,
                price = :price,
                maintenanceStatus = :maintenanceStatus,
                omiZone = :omiZone,
                unitFloor = :unitFloor,
                energyClass = :energyClass,
                lastPriceDate = :lastPriceDate,
                pricePsm = :pricePsm,
                latitude = :latitude, 
                longitude = :longitude,
                predPricePsm = :predPricePsm,
                predPriceDelta = :predPriceDelta,
                predRenewedPricePsm = :predRenewedPricePsm,
                predNewarcRenewedPricePsm = :predNewarcRenewedPricePsm,
                predOfferPrice = :predOfferPrice,
                predMinDiscount = :predMinDiscount,
                predSellingPrice = :predSellingPrice,
                predCosts = :predCosts,
                predGrossMargin= :predGrossMargin,
                planimetry = :planimetry
            WHERE id = :id;
            '''
            update_query = sqlalchemy.text(update_query)
            params = {'lastSeenDate': old_comp['lastSeenDate'],
                      'timesSeen': old_comp['timesSeen'],
                      'price': old_comp['price'],
                      'maintenanceStatus': old_comp['maintenanceStatus'],
                      'omiZone': old_comp['omiZone'],
                      'unitFloor':  old_comp['unitFloor'],
                      'energyClass': old_comp['energyClass'],
                      'lastPriceDate': old_comp['lastPriceDate'],
                      'pricePsm': old_comp['pricePsm'],
                      'latitude': old_comp['latitude'],
                      'longitude': old_comp['longitude'],
                      'predPricePsm': old_comp['predPricePsm'],
                      'predPriceDelta': old_comp['predPriceDelta'],
                      'predRenewedPricePsm': old_comp['predRenewedPricePsm'],
                      'predNewarcRenewedPricePsm': old_comp['predNewarcRenewedPricePsm'],
                      'predOfferPrice': old_comp['predOfferPrice'],
                      'predMinDiscount': old_comp['predMinDiscount'],
                      'predSellingPrice': old_comp['predSellingPrice'],
                      'predCosts': old_comp['predCosts'],
                      'predGrossMargin': old_comp['predGrossMargin'],
                      'planimetry': old_comp['planimetry'],
                      'id': old_comp['id']}
            conn.execute(update_query, parameters=params)
            conn.commit()
            is_it_duplicate += 1
        # when comparabile is not a duplicate
        else:
            # add row to db
            all_cols = [key for key in IMMOBILIARE_SCRAPING_DICT.keys() if key != 'id']
            all_cols_str = ', '.join(all_cols)
            str_formatting_list = [f':{key}' for key in all_cols]
            str_formatting = ', '.join(str_formatting_list)
            insert_into_query = f'''
            INSERT INTO {DB_TABLE_NAME} ({all_cols_str}) VALUES ({str_formatting});
                        '''
            params = {col: comp.get(col) for col in all_cols}
            insert_into_query = sqlalchemy.text(insert_into_query)
            conn.execute(insert_into_query, parameters=params)
            conn.commit()
            is_it_new += 1
        conn.close()

    return is_it_duplicate, is_it_new
    

@time_it
def scrape_city_immobiliare(regione: str, town: str, opts) -> None:
    '''
    Scrape info from immobiliare.it city defined by its region and its city and saves to DB.

    Parameters
    ----------
    regione: str
        region in which the city lives
    town: str
        city to scrape in region
    opts: argparse.Namespace
        options for scraping

    '''
    regione = regione.lower()
    town = town.lower()

    print('---------------')
    print(f'{town.upper()}, {regione.upper()}')
    print('scrape_city_immobiliare started!')

    # get city's marketZones according to immobiliare. Needed to encode url for request.
    marketZones = get_immobiliare_marketZones(regione, town)
    fetched_links_counter = 0
    brand_new_links_counter = 0
    price_updated_links_counter = 0
    duplicate_links_counter = 0
    for marketZone in marketZones:
        print('++++++++++++++++')
        print(marketZone.upper())
        print('++++++++++++++++')
        # get links of immobiliare ads for city and marketZone
        links_n_prices = scrape_immobiliare_ads_links_in_city_marketZone(town, marketZone)
        print(len(links_n_prices), 'links were fetched')
        fetched_links_counter += len(links_n_prices)
        # get new links or links whose price has been updated
        brand_new_links, price_updated_links, duplicate_links = get_new_and_price_updated_links_update_others(links_n_prices, town)
        brand_new_links_counter += len(brand_new_links)
        price_updated_links_counter += len(price_updated_links)
        duplicate_links_counter += len(duplicate_links)
        to_be_scraped_links = brand_new_links + price_updated_links
        print(len(to_be_scraped_links), 'links are to be scraped')
        if len(to_be_scraped_links) > 0:
            # Create Threads
            # scrape_immobiliare_ads(to_be_scraped_links, 69, opts)
            # Define number of threads based on the number of links
            nthreads = min(6, max(1, len(to_be_scraped_links)))
            print(f'threads used: {nthreads}')
            threads = [Thread(target=scrape_immobiliare_ads, args=(link_chunk, i+1, opts)) for i, link_chunk in enumerate(np.array_split(to_be_scraped_links, nthreads))]
            for t in threads:
                t.start()
            for t in threads:
                t.join()
    print('----------------------')
    print(regione.upper(), town.upper())
    print('----------------------')
    print(f'Out of {fetched_links_counter} links fetched, {brand_new_links_counter} were new, {price_updated_links_counter + duplicate_links_counter} where duplicates.')
    print(f'Out of {price_updated_links_counter + duplicate_links_counter} duplicates, {price_updated_links_counter} had their price updated and {duplicate_links_counter} were simple duplicates.')
    print(f'Out of {brand_new_links_counter + price_updated_links_counter} links to be scraped, {scraped_comparabili} were scraped.')
    print(f'{duplicate_comparabili + new_comparabili} were succesfully updated/saved,')
    print(f'among those {duplicate_comparabili} were updates, {new_comparabili} were new')
    print('----------------------')
    print(f'{asked_requests} requests were made, {answered_requests} were answered')
    print('----------------------')


@time_it
def get_marketPricePsm_marketPriceDelta_newarcScore(town: str) -> None:
    '''
    For every record in db, this method computes their respective marketPricePsm, marketPriceDelta and newarcScore and
    saves these values in the respective column of db.

    Parameters
    ----------
    town: str
        city in which we will perform this computation
    '''
    # connecting to db
    try:
        with scraping_db.connect() as conn:
            # use db
            query = f'''USE {DB_NAME};'''
            query = sqlalchemy.text(query)
            conn.execute(query)
            conn.commit()
            # selecting inputs in city with valid location that have been updated in last TIME_DELTA days
            selecting_query = f'''
            SELECT * FROM {DB_TABLE_NAME} 
            WHERE city=:city
            AND latitude IS NOT NULL
            AND longitude IS NOT NULL
            AND streetName IS NOT NULL
            AND streetNum IS NOT NULL
            AND lastPriceDate > DATE(:today) - {TIME_DELTA.days};
            '''
            params = {'city': town, 'today': TODAY}
            selecting_query = sqlalchemy.text(selecting_query)
            results = conn.execute(selecting_query, parameters=params).fetchall()
            # clean data we want to use as comparables
            all_data_df = pd.DataFrame(results)
            all_data_df = all_data_df.replace({np.nan: None})
            all_data_df = all_data_df.loc[~all_data_df['sellingAgency'].str.match('.*PLAN BUY.*', na=False)]
            all_data_df = all_data_df.loc[~all_data_df['sellingAgency'].str.match('.*AsteXTe.*', na=False)]
            all_data_df = all_data_df.loc[~all_data_df['sellingAgency'].str.match('.*Gold Point.*', na=False)]
            all_data_df = all_data_df.drop_duplicates(subset=DISCR_COLS)
            print(len(all_data_df), 'all_data_df len')
            # define data for which we want to compute market metrics
            results = all_data_df.loc[all_data_df['lastPriceDate'] > datetime.strptime(TODAY, '%Y/%m/%d').date() - timedelta(days=7)].to_dict(orient="records")
            print(len(results), 'results len')
            conn.close()
    except Exception as e:
        print(f'Error in connecting to db in method get_marketPricePsm_marketPriceDelta_newarcScore:\n{e}')
    # for every record in city
    for res in results:
        try:
            # get comps for actual and future house maintenanceStatus
            comps = get_cma(res, all_data_df)
            comps_df = pd.DataFrame(comps)
            restructured_res = res.copy()
            if res['maintenanceStatus'] not in ['Ottimo / Ristrutturato', 'Nuovo / In costruzione']:
                restructured_res['maintenanceStatus'] = 'Ottimo / Ristrutturato'
            restructured_comps = get_cma(restructured_res, all_data_df)
            restructured_comps_df = pd.DataFrame(restructured_comps)
            # if comps are found update marketPricePsm, marketPriceDelta, renewedMarketPricePsm
            if len(comps_df) > 0:
                res['marketPricePsm'] = comps_df['pricePsm'].mean(skipna=True)
                if math.isnan(res['marketPricePsm']):
                    res['marketPricePsm'] = None
                if res['marketPricePsm'] != 0 and res['pricePsm'] != 0 and res['pricePsm'] is not None and res['marketPricePsm'] is not None:
                    res['marketPriceDelta'] = np.round((res['pricePsm'] - res['marketPricePsm'])/res['marketPricePsm'], 2)
            if len(restructured_comps_df) > 0:
                if len(restructured_comps_df) > 2:
                    res['renewedMarketPricePsm'] = restructured_comps_df['pricePsm'].quantile(0.7)
                else:
                    res['renewedMarketPricePsm'] = restructured_comps_df['pricePsm'].mean(skipna=True)
                    if math.isnan(res['renewedMarketPricePsm']):
                        res['renewedMarketPricePsm'] = None
            # compute marketPricePsm features
            res['offerPrice'],  res['minDiscount'], res['sellingPrice'], res['costs'], res['grossMargin'] = (
                get_conto_economico(res, 'renewedMarketPricePsm'))
            # compute newarc score
            res['newarcScore'] = get_newarcScore(res)
            # update row with computed infos
            with scraping_db.connect() as conn:
                # update row with market info
                update_query = f'''
                UPDATE {DB_TABLE_NAME} 
                SET marketPricePsm = :marketPricePsm, 
                marketPriceDelta = :marketPriceDelta, 
                renewedMarketPricePsm = :renewedMarketPricePsm, 
                newarcScore = :newarcScore, 
                offerPrice = :offerPrice,
                minDiscount = :minDiscount,
                sellingPrice = :sellingPrice,
                costs = :costs,
                grossMargin = :grossMargin
                WHERE id = :id;
                '''
                update_query = sqlalchemy.text(update_query)
                query_params = {'marketPricePsm': res['marketPricePsm'],
                                'marketPriceDelta': res['marketPriceDelta'],
                                'renewedMarketPricePsm': res['renewedMarketPricePsm'],
                                'newarcScore': res['newarcScore'],
                                'offerPrice': res['offerPrice'],
                                'minDiscount': res['minDiscount'],
                                'sellingPrice': res['sellingPrice'],
                                'costs': res['costs'],
                                'grossMargin': res['grossMargin'],
                                'id': res['id']}
                conn.execute(update_query, parameters=query_params)
                conn.commit()
                conn.close()
        except Exception as e:
            print(f'Error in updating db in get_marketPricePsm_marketPriceDelta_newarcScore:\n{e}')
            try:
                conn.close()
            except Exception as e:
                print(f'Error in get_marketPricePsm_marketPriceDelta_newarcScore:\n{e}')


def get_cma(comp: dict, data: pd.DataFrame) -> list[dict]:
    '''
    Returns list of dicts with comps found in data for certain input comp.

    Parameters
    ----------
    comp: dict
        house for which we desire to retrieve comps
    data: pd.DataFrame
        data among which we look for comps for input comp

    Returns
    -------
    comps: list[dict]
        list of comps found for input comp
    '''
    comp_list = list()
    # select comps
    comps = data.loc[(data['city'] == comp['city']) &
                     (data['marketZone'] == comp['marketZone']) &
                     (data['maintenanceStatus'] == comp['maintenanceStatus']) &
                     (data['id'] != comp['id']) &
                     (data['grossSquareFootage'] > 13)]
    # if comps are found
    if len(comps) > 0:
        comps.insert(len(comps.columns), 'distance', None)
        for index, row in comps.iterrows():
            comps.loc[index, 'distance'] = utils.haversine_distance(row.latitude, row.longitude,
                                                                    comp['latitude'], comp['longitude'])
        # select latest comps with a distance (and time) threshold
        comps = comps.loc[comps['distance'] < DISTANCE_DELTA]
        # comps = comps.loc[datetime.strptime(TODAY, '%Y/%m/%d').date() - comps['lastSeenDate'] > timedelta(days=2)]
        # if comps are at least 3 return comps else return empty list
        if len(comps) > 2:
            comp_list = comps.to_dict(orient='records')
    return comp_list


def get_newarcScore(comp: dict) -> int:
    '''
    Computes newarc score for input comp.

    Parameters
    ----------
    comp: dict
        house for which we want to compute newarcScore

    Returns
    -------
    score: int
        newarcScore for input comp, must be in [0,100]
    '''
    score = 100
    # price
    if comp['marketPriceDelta'] is not None:
        if comp['marketPriceDelta'] < -0.15:
            score -= 0
        elif comp['marketPriceDelta'] <= -0.1:
            score -= 10
        elif comp['marketPriceDelta'] > -0.1:
            score -= 30
    # garage, cantina, concierge
    if comp['hasGarage'] == 0:
        score -= 3
    if comp['hasCantina'] == 0:
        score -= 5
    if comp['hasConcierge'] == 0:
        score -= 2
    # unitFloor, elevator
    if comp['unitFloor'] is not None:
        if comp['unitFloor'].lower() in ['piano rialzato', 'piano terra', 'ammezzato']:
            score -= 20
        elif comp['unitFloor'].lower() == '1':
            if comp['elevator'] == 1:
                score -= 10
            else:
                score -= 15
        elif comp['unitFloor'].lower() == '2':
            if comp['elevator'] == 1:
                score -= 0
            else:
                score -= 20
        elif re.match('[0-9]+', comp['unitFloor']):
            if comp['elevator'] == 1:
                score -= 0
            else:
                score -= 30
        elif comp['unitFloor'].lower() == 'ultimo':
            if comp['elevator'] == 1:
                score -= 0
            else:
                score -= 30
    return int(score)


def should_scraping_run() -> bool:
    day_today = date.today().weekday()
    day_dict = {0: 'Monday', 1: 'Tuesday', 2: 'Wednesday', 3: 'Thursday', 4: 'Friday', 5: 'Saturday', 6: 'Sunday'}
    print(f'TODAY IS {day_dict[day_today].upper()}')
    if day_today in [1, 3]:
        return True
    else:
        return False


if __name__ == '__main__':
    start = time.time()
    parser = argparser.get_argparser()
    opts, _ = parser.parse_known_args()
    print('-------------------------------------')
    print('----- SCRAPING PROCEDURE CALLED -----')
    print('-------------------------------------')
    if not should_scraping_run():
        print('SCRAPING SHOULD NOT RUN TODAY')
        print('-------------------------------------')
        print(f'Scraping Procedure took {(time.time() - start)/60.} minutes to run')
        print('-------------------------------------')
    else:
        print('SCRAPING TIME!')
        print('-------------------------------------')
        print('-----  SETTING UP DB CONNECTION  ----')
        print('-------------------------------------')
        init_db_connection()
        create_db_table_mysql()
        print('--- SETTING UP SCRAPING PROCEDURE ---')
        print('opts.scrapingCity')
        print(opts.scrapingCity)
        region_city_dict = {'Piemonte': ['Torino'],
                            'Lombardia': ['Milano'],
                            'Lazio': ['Roma'],
                            'Friuli-Venezia-Giulia': ['Trieste']}
        region, city = None, None
        for item in region_city_dict.items():
            if opts.scrapingCity.capitalize() in item[1]:
                region = item[0]
                city = opts.scrapingCity.capitalize()
        if region is not None and city is not None:
            print('---- SCRAPING PROCEDURE STARTED! ----')
            print(f'------ SCRAPING {region, city} ------')
            scrape_city_immobiliare(region, city, opts)
            get_marketPricePsm_marketPriceDelta_newarcScore(city)
            print('-------------------------------------')
            print('----- SCRAPING PROCEDURE ENDED! -----')
        else:
            print('Problem with scrapingCity parameter: must be one among Torino, Milano, Roma')
        print('------------------------------------')
        print(f'Scraping Procedure took {(time.time() - start)/60.} minutes to run')
        print('------------------------------------')

