import os
import json
import base64
import firebase_admin
from firebase_admin import credentials, firestore, storage


def initFirebase():
    if not firebase_admin._apps:
        # firebase_str = os.environ.get('FIREBASE_STAGING_ADMINSDK')
        # firebase_json = json.loads(firebase_str)
        firebase_str = *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        firebase_json = firebase_str
        cred = credentials.Certificate(firebase_json)
        firebase_admin.initialize_app(cred)


def addDoc(collection: str, doc: dict) -> str:
    try:
        firestore_db = firestore.client()
        doc_ref = firestore_db.collection(collection).document()
        doc_ref.set(doc)
        return doc_ref.id
    except Exception as e:
        print(e)


def updateDocKey(collection: str, firestore_id: str, key: str, value):
    try:
        firestore_db = firestore.client()
        doc_ref = firestore_db.collection(collection).document(firestore_id)
        doc_ref.update({key: value})
        return doc_ref.id
    except Exception as e:
        print(e)

def getDoc(collection: str, firestore_id: str) -> dict:
    try:
        firestore_db = firestore.client()
        doc_ref = firestore_db.collection(collection).document(firestore_id)
        doc = doc_ref.get().to_dict()
        return doc
    except Exception as e:
        print(e)

def getImageFromStorage(pic_path: str) -> str:
    try:
        bucket = storage.bucket(name=os.environ.get('FIRESTORE_BUCKET_NAME'))
        blob = bucket.blob(pic_path)
        pic_bytes = blob.download_as_bytes()
        pic_base64: str = base64.b64encode(pic_bytes).decode('utf-8')
        return pic_base64
    except Exception as e:
        print(e)

def saveImageToStorage(pic_base64: str, pic_path: str, filename: str):
    try:
        bucket = storage.bucket(name=os.environ.get('FIRESTORE_BUCKET_NAME'))
        blob = bucket.blob(pic_path)
        pic_bytes = base64.b64decode(pic_base64)
        blob.upload_from_string(pic_bytes, content_type='image/png')
        blob.content_disposition = f'attachment; filename="{filename}"'
        blob.patch()
    except Exception as e:
        print(e)

def example():
    dataFromWebsite = {}
    addDoc('testValuatorSubmissions', dataFromWebsite)