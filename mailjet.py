from mailjet_rest import Client
from utils import dots_as_thousands_separator


class MailJet(Client):
    '''
    Wrapper around :code:`Client object` from :code:`mailjet_rest` API,
    see `MailJet API docs <https://dev.mailjet.com/email/guides/>`_.

    Invoked to send an automatic email to a certain recipient's email.

    Must be first instantiated with :code:`auth = (api_key, api_secret)` and :code:`version = 'v3.1'`
    as arguments. MailJet's keys are now hard coded in :code:`app.py`, must be changed.
    '''

    def send_email_new_prediction(self,
                                  recipient_email: str,
                                  recipient_name: str,
                                  date: str,
                                  address: str,
                                  rooms: int,
                                  squaredmeters: int,
                                  predvalue: int
                                  ):
        '''
        Sends simple templated email to a recipient of <NAME_EMAIL> as sender and
        a template used to comunicate the recipient that an house with data
        :code:`date, address, rooms, squaredmeters, predvalue` has just been evaluated.

        Templates are invocked through :code:`template_id` local variable and
        are stored in Newarc's MailJet account.

        Parameters
        ----------
        recipient_email: str
            email to which send the email to
        recipient_name: str
            name of the contact we're sending this email to
        date: str
            UTC datetime of the prediction
        address: str
            comparabile' address and street number
        rooms: int
            number of rooms ('locali') in the comparabile
        squaredmeters: int
            how many squared meters the comparabile is
        predvalue: int
            algorithm prediction for this particular comparabile

        Returns
        -------
        result: Object
            result object after sending API call to MailJet, brings info
            on the status of the emails sent
        -------
        '''

        recipient_email = recipient_email
        recipient_name = recipient_name

        sender_email = '<EMAIL>'
        sender_name = 'NEWARC'

        template_id = 4586168

        template_variables = {'date': date,
                              'address': address,
                              'rooms': str(rooms),
                              'squaredmeters': str(squaredmeters),
                              'predvalue': dots_as_thousands_separator(predvalue)
                              }

        data = {
            'Messages': [
                {
                    "From": {
                        "Email": sender_email,
                        "Name": sender_name
                    },
                    "To": [
                        {
                            "Email": recipient_email,
                            "Name": recipient_name
                        }
                    ],
                    "TemplateID": template_id,
                    "TemplateLanguage": True,
                    "Subject": "Nuova valutazione immobile",
                    "Variables": template_variables,
                }
            ]
        }

        result = self.send.create(data=data)

        return result
    

    def send_email_immagina_smart_completion(self,
                                  recipient_email: str,
                                  recipient_name: str,
                                  project_id: str,
                                  ):
        
        recipient_email = recipient_email
        recipient_name = recipient_name

        sender_email = '<EMAIL>'
        sender_name = 'NEWARC'

        template_id = 7187669

        template_variables = {"projectid": project_id,}

        data = {
            'Messages': [
                {
                    "From": {
                        "Email": sender_email,
                        "Name": sender_name
                    },
                    "To": [
                        {
                            "Email": recipient_email,
                            "Name": recipient_name
                        }
                    ],
                    "TemplateID": template_id,
                    "TemplateLanguage": True,
                    "Subject": "Generazione completata",
                    "Variables": template_variables,
                }
            ]
        }

        result = self.send.create(data=data)

        return result
    
    def send_email_smart_generation_error(self,
                                          mode: str,
                                          project_id: str,
                                          recipient_email: str,
                                          recipient_name: str,
                                          agency_name: str = None,
                                          error_msgs: str = None,
                                  ):
        _modes = ['workside', 'userside']
        if mode not in _modes:
            raise ValueError(f'mode must be one among {_modes}')
        if mode == 'workside' and (error_msgs is None or agency_name is None):
            raise ValueError('error_msgs and agency_name must be provided if mode is workside')
        
        email_template_ids = {'workside': 7197099, 'userside': 7197304}
        email_template_id = email_template_ids[mode]
        
        recipient_email = recipient_email
        recipient_name = recipient_name

        sender_email = '<EMAIL>'
        sender_name = 'NEWARC'

        failure_origin = "heroku server - generation error"
        template_variables = {"projectid": project_id,}
        if mode == 'workside':
            template_variables["agencyname"] = agency_name
            template_variables['failureorigin'] = failure_origin
            template_variables['generationerror'] = error_msgs

        data = {
            'Messages': [
                {
                    "From": {
                        "Email": sender_email,
                        "Name": sender_name
                    },
                    "To": [
                        {
                            "Email": recipient_email,
                            "Name": recipient_name
                        }
                    ],
                    "TemplateID": email_template_id,
                    "TemplateLanguage": True,
                    "Subject": "Generazione completata",
                    "Variables": template_variables,
                }
            ]
        }

        result = self.send.create(data=data)

        return result


