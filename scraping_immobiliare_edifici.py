import time
import argparser
from utils import connect_with_connector, time_it, OmiZone
from consts import (DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, SCRAPER_SQL_GOOGLE_CREDENTIALS,
                    EDIFICI_TABLE_NAME, EDIFICI_TABLE_DICT, TODAY)
from scraping_immobiliare import remove_nonnumbers_from_string
import requests
from bs4 import BeautifulSoup
from scraping_immobiliare import random_sleep
import json
import re
import sqlalchemy
import pandas as pd


scraping_db = None
def init_db_connection() -> None:
    """Initiates connection to database and its' structure."""
    global scraping_db
    if scraping_db is None:
        scraping_db = connect_with_connector(DB_CONNECTION_NAME_WRITE, DB_USER, DB_PASSWORD, DB_NAME, SCRAPER_SQL_GOOGLE_CREDENTIALS)

@time_it
def scrape_edifici_links_from_city_immobiliare(town: str) -> list[str]:
    url_fetch_links = f'https://www.immobiliare.it/vendita-palazzi/{town}/?criterio=data&ordine=desc&noAste=1'
    pag = 0
    links_list = []
    total_ads = 0
    while True:
        try:
            random_sleep()
            pag += 1
            link = url_fetch_links + f'&pag={pag}'
            print(link)
            res = requests.get(link)
            if res.status_code != 200:
                print(f'Error: status code {res.status_code} in request in scrape_edifici_links_from_city_immobiliare for link {link}')
            soup = BeautifulSoup(res.text, "html.parser")
            # check if last page was reached
            error_data = soup.find('div',
                                   class_='nd-alert nd-alert--warning in-errorMessage__alert in-errorMessage__title')
            if error_data:
                if re.match('.*non è più disponibile.*', error_data.text.lower()):
                    # last page reached, stop scraping
                    print('end of edifici pages')
                    break
            magic_script = soup.find('script', id="__NEXT_DATA__")
            data_dict = json.loads(magic_script.text)
            data_list = data_dict['props']['pageProps']['dehydratedState']['queries'][0]['state']['data']['results']
            total_ads += len(data_list)
            # get edifici links
            for edificio in data_list:
                if 'seo' in edificio:
                    if 'url' in edificio['seo']:
                        links_list.append(edificio['seo']['url'])
            # avoid infinite loops
            if pag > 100:
                print('Breaking for loop in scrape_edifici_links_from_city_immobiliare for pag limit reached')
                break
        except Exception as e:
            print(f'Error in scrape_edifici_links_from_city_immobiliare: {e}')
    print(f'Found {total_ads} edifici')
    return links_list

@time_it
def scrape_edifici_ads_immobiliare(ads_links: list[str]) -> list[dict]:
    scraped_ads = 0
    links_dict_list = list()
    for url in ads_links:
        try:
            random_sleep()
            res = requests.get(url)
            if res.status_code != 200:
                print(f'Error: status code {res.status_code} in request in scrape_edifici_ads_immobiliare for link {url}')
            else:
                soup = BeautifulSoup(res.text, "html.parser")
                magic_script = soup.find('script', id="__NEXT_DATA__")
                data_dict = json.loads(magic_script.text)
                data_dict = data_dict['props']['pageProps']['detailData']['realEstate']
                price, title, latitude, longitude, gSF, description = None, None, None, None, None, None
                sellingAgency, sellingAgencyUrl, marketZone, maintenanceStatus = None, None, None, None
                rooms, numberOfBathrooms, unitFloor, siteId, propertyType = None, None, None, None, None
                city, streetName, streetNum, buildingFloorCount, constructionYear = None, None, None, None, None
                airConditioning, energyClass, heatingType, heatingEmitter, heatingFuel = False, None, None, None, None
                images_dict, planimetry_dict = str(dict()), str(dict())
                if 'id' in data_dict:
                    siteId = data_dict['id']
                if 'price' in data_dict:
                    if 'value' in data_dict['price']:
                        price = data_dict['price']['value']
                if 'title' in data_dict:
                    title = data_dict['title']
                if 'properties' in data_dict:
                    if len(data_dict['properties']) > 0:
                        if 'location' in data_dict['properties'][0]:
                            if 'microzone' in data_dict['properties'][0]['location']:
                                marketZone = data_dict['properties'][0]['location']['microzone']
                            if 'address' in data_dict['properties'][0]['location']:
                                streetName = data_dict['properties'][0]['location']['address']
                            if 'streetNumber' in data_dict['properties'][0]['location']:
                                streetNum = data_dict['properties'][0]['location']['streetNumber']
                            if 'city' in data_dict['properties'][0]['location']:
                                city = data_dict['properties'][0]['location']['city']
                            if 'latitude' in data_dict['properties'][0]['location']:
                                latitude = data_dict['properties'][0]['location']['latitude']
                                if latitude is not None:
                                    latitude = float(data_dict['properties'][0]['location']['latitude'])
                            if 'longitude' in data_dict['properties'][0]['location']:
                                longitude = data_dict['properties'][0]['location']['longitude']
                                if longitude is not None:
                                    longitude = float(data_dict['properties'][0]['location']['longitude'])
                        if 'surface' in data_dict['properties'][0]:
                            gSF = int(data_dict['properties'][0]['surface'].replace(' m²',
                                                                                    '').replace('.',
                                                                                                ''))
                        if 'multimedia' in data_dict['properties'][0]:
                            if 'photos' in data_dict['properties'][0]['multimedia']:
                                if len(data_dict['properties'][0]['multimedia']['photos']) > 0:
                                    images_dict = str({obj['urls']['medium']: obj['caption'] for obj in
                                                       data_dict['properties'][0]['multimedia']['photos']})
                            if 'floorplans' in data_dict['properties'][0]['multimedia']:
                                if len(data_dict['properties'][0]['multimedia']['floorplans']) > 0:
                                    planimetry_dict = str({obj['urls']['medium']: obj['caption'] for obj in
                                                       data_dict['properties'][0]['multimedia']['floorplans']})
                        if 'ga4Condition' in data_dict['properties'][0]:
                            maintenanceStatus = data_dict['properties'][0]['ga4Condition']
                        if 'rooms' in data_dict['properties'][0]:
                            rooms = remove_nonnumbers_from_string(data_dict['properties'][0]['rooms'])
                        if 'bathrooms' in data_dict['properties'][0]:
                            numberOfBathrooms = remove_nonnumbers_from_string(
                                data_dict['properties'][0]['bathrooms'])
                        if 'floor' in data_dict['properties'][0]:
                            if 'floorOnlyValue' in data_dict['properties'][0]['floor']:
                                unitFloor = data_dict['properties'][0]['floor']['floorOnlyValue']
                        if 'floors' in data_dict['properties'][0]:
                            buildingFloorCount = remove_nonnumbers_from_string(
                                data_dict['properties'][0]['floors'])
                        if 'typologyGA4Translation' in data_dict['properties'][0]:
                            propertyType = data_dict['properties'][0]['typologyGA4Translation']
                        if 'buildingYear' in data_dict['properties'][0]:
                            constructionYear = data_dict['properties'][0]['buildingYear']
                        if 'energy' in data_dict['properties'][0]:
                            if 'class' in data_dict['properties'][0]['energy']:
                                energyClass = data_dict['properties'][0]['energy']['class']
                            if 'heatingType' in data_dict['properties'][0]['energy']:
                                heating_list = data_dict['properties'][0]['energy']['heatingType'].split(',')
                                if len(heating_list) == 3:
                                    heatingType = heating_list[0].strip()
                                    heatingEmitter = heating_list[1].strip()
                                    heatingFuel = heating_list[2].strip()
                                else:
                                    heatingType = heating_list[0].strip()
                            if 'airConditioning' in data_dict['properties'][0]['energy']:
                                airConditioning = True
                        if 'description' in data_dict['properties'][0]:
                            description = data_dict['properties'][0]['description']
                if 'advertiser' in data_dict:
                    if 'agency' in data_dict['advertiser'].keys():
                        if 'displayName' in data_dict['advertiser']['agency'].keys():
                            sellingAgency = data_dict['advertiser']['agency']['displayName']
                        if 'agencyUrl' in data_dict['advertiser']['agency'].keys():
                            sellingAgencyUrl = data_dict['advertiser']['agency']['agencyUrl']
                    elif 'supervisor' in data_dict['advertiser'].keys():
                        if 'type' in data_dict['advertiser']['supervisor'].keys():
                            if data_dict['advertiser']['supervisor']['type'] == 'user':
                                sellingAgency = 'privato'
                                if 'phones' in data_dict['advertiser']['supervisor'].keys():
                                    if len(data_dict['advertiser']['supervisor']['phones']) > 0:
                                        if data_dict['advertiser']['supervisor']['phones'][0] is dict:
                                            if 'value' in data_dict['advertiser']['supervisor']['phones'][0].keys():
                                                sellingAgencyUrl = \
                                                data_dict['advertiser']['supervisor']['phones'][0]['value']
                omiZone = None
                if city is not None:
                    omiZone_obj = OmiZone(f"data/{city.capitalize()}/{city.capitalize()}_OmiZones_2022.kml")
                    if latitude is not None and longitude is not None:
                        omiZone = omiZone_obj.getOmiZone(longitude, latitude)
                pricePsm = None
                if gSF is not None and price is not None:
                    pricePsm = price / (gSF + 1.e-5)
                mini_data = dict(link=url,
                                 siteId=siteId,
                                 propertyType=propertyType,
                                 title=title,
                                 description=description,
                                 streetName=streetName,
                                 streetNum=streetNum,
                                 city=city,
                                 marketZone=marketZone,
                                 omiZone=omiZone,
                                 latitude=latitude,
                                 longitude=longitude,
                                 maintenanceStatus=maintenanceStatus,
                                 rooms=rooms,
                                 numberOfBathrooms=numberOfBathrooms,
                                 unitFloor=unitFloor,
                                 buildingFloorCount=buildingFloorCount,
                                 constructionYear=constructionYear,
                                 energyClass=energyClass,
                                 heatingType=heatingType,
                                 heatingEmitter=heatingEmitter,
                                 heatingFuel=heatingFuel,
                                 airConditioning=airConditioning,
                                 grossSquareFootage=gSF,
                                 price=price,
                                 pricePsm=pricePsm,
                                 firstPriceDate=TODAY,
                                 lastSeenDate=TODAY,
                                 lastPriceDate=TODAY,
                                 timesSeen=1,
                                 sellingAgency=sellingAgency,
                                 sellingAgencyUrl=sellingAgencyUrl,
                                 imagesUrls=images_dict,
                                 planimetry=planimetry_dict)
                links_dict_list.append(mini_data)
                scraped_ads += 1
        except Exception as e:
            print(f'Error in scrape_edifici_ads_immobiliare: {e}')
    print(f'{scraped_ads} edifici links out of {len(ads_links)} were scraped')
    return links_dict_list

@time_it
def save_to_db_mysql(dict_list: list[dict]) -> None:
    saved_ads = 0
    duplicates_num = 0
    for dict_row in dict_list:
        try:
            with scraping_db.connect() as conn:
                # creating db if it doesn't exist
                create_db_query = f'''CREATE DATABASE IF NOT EXISTS {DB_NAME};'''
                conn.execute(sqlalchemy.text(create_db_query))
                conn.commit()
                # select db
                use_db_query = f'USE {DB_NAME}'
                conn.execute(sqlalchemy.text(use_db_query))
                conn.commit()
                # create table if it doesn't exist
                table_def_string = ''
                for key in EDIFICI_TABLE_DICT.keys():
                    table_def_string += key + ' ' + EDIFICI_TABLE_DICT[key] + ','
                table_def_string = table_def_string.strip(',')
                create_table_query = f'''CREATE TABLE IF NOT EXISTS {EDIFICI_TABLE_NAME} ({table_def_string});'''
                conn.execute(sqlalchemy.text(create_table_query))
                conn.commit()
                # save ad to sql db
                # find out if record is already on db
                query = f"SELECT * FROM {EDIFICI_TABLE_NAME} WHERE link = '{dict_row['link']}' ORDER BY firstPriceDate DESC"
                duplicates = conn.execute(sqlalchemy.text(query)).fetchall()
                duplicates = pd.DataFrame(duplicates).to_dict(orient='records')
                if len(duplicates) > 0:
                    if len(duplicates) > 1:
                        print(f"There's more than one duplicate for link: {dict_row['link']}")
                    old_comp = duplicates[0]
                    old_comp['lastSeenDate'] = TODAY
                    old_comp['timesSeen'] += 1
                    if dict_row['price'] is not None:
                        if old_comp['price'] is None:
                            old_comp['price'] = 0
                        if not old_comp['price'] * 0.95 < float(dict_row['price']) < old_comp['price'] * 1.05:
                            old_comp['price'] = float(dict_row['price'])
                            old_comp['lastPriceDate'] = TODAY
                            old_comp['pricePsm'] = old_comp['price'] / float(old_comp['grossSquareFootage'])
                    # update old row with new information
                    update_query = f'''UPDATE {EDIFICI_TABLE_NAME} 
                                                       SET lastSeenDate = :lastSeenDate,
                                                           lastPriceDate = :lastPriceDate, 
                                                           timesSeen = :timesSeen,
                                                           price = :price,
                                                           pricePsm = :pricePsm
                                                       WHERE id = :id;'''
                    conn.execute(sqlalchemy.text(update_query), parameters=old_comp)
                    conn.commit()
                    duplicates_num += 1
                else:
                    all_cols = [key for key in EDIFICI_TABLE_DICT.keys() if key != 'id']
                    all_cols_str = ', '.join(all_cols)
                    str_formatting_list = [f':{key}' for key in all_cols]
                    str_formatting = ', '.join(str_formatting_list)
                    insert_into_query = f'''
                    INSERT INTO {EDIFICI_TABLE_NAME} ({all_cols_str}) VALUES ({str_formatting});
                    '''
                    conn.execute(sqlalchemy.text(insert_into_query), dict_row)
                    conn.commit()
                saved_ads += 1
        except Exception as e:
            print(f'Error in save_to_db_mysql: {e}')
            conn.close()
    print(f'out of {len(dict_list)} edifici ads {saved_ads} were successfully saved to db, {saved_ads - duplicates_num} were new ads, {duplicates_num} were duplicates')


if __name__ == '__main__':
    start = time.time()
    parser = argparser.get_argparser()
    opts, _ = parser.parse_known_args()
    print('-------------------------------------')
    print('-----  SETTING UP DB CONNECTION  ----')
    print('-------------------------------------')
    init_db_connection()
    print('----SETTING UP SCRAPING PROCEDURE----')
    print('opts.scrapingCity')
    print(opts.scrapingCity)
    city = opts.scrapingCity
    if city is None:
        print(f'Error: scrapingCity parameter must be not None String among: {opts.valid_cities}')
        print('Found:', opts.scrapingCity)
    elif city.capitalize() not in opts.valid_cities:
        print(f'Error: scrapingCity parameter must be a String among: {opts.valid_cities}')
        print('Found:', opts.scrapingCity)
    else:
        print('----SCRAPING EDIFICI PROCEDURE STARTED!----')
        print(f'----SCRAPING EDIFICI in {city.capitalize()}----')
        ad_links = scrape_edifici_links_from_city_immobiliare(city)
        # ad_links = [data for data in ad_links if data == 'https://www.immobiliare.it/annunci/113228759/']
        ad_dicts = scrape_edifici_ads_immobiliare(ad_links)
        save_to_db_mysql(ad_dicts)
        print('----SCRAPING EDIFICI PROCEDURE ENDED!----')
        print('------------------------------------')
        print(f'Scraping Procedure took {(time.time() - start) / 60.:.2f} minutes to run')