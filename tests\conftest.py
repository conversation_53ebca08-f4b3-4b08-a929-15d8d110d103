import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import pytest
from app import app


@pytest.fixture
def client():
    app.config["TESTING"] = True
    app.testing = True
    with app.test_client() as client:
        yield client


@pytest.fixture
def correct_predict_call():
    data = dict()
    data['addressObject'] = dict()
    data['comparabili'] = list()
    data['status'] = ''
    data['typology'] = ''
    data['planimetry'] = dict()
    data['additionalInfo'] = dict()
    data['planimetry'] = {
        'surface': 120,
        'locali': 3,
        'bathrooms': 1,
        'floor': 2,
        'numFloors': 4,
        'hasElevator': True,
        'hasBalcony': True,
        'numBalcony': 4,
        'hasTerrace': False,
        'hasCloset': False,
        'hasSharedGarden': False,
        'hasPrivateGarden': False,
        'gardenMq': 0,
        'hasGarage': False,
        'numGarage': 0,
        'hasCantina': True,
        'numCantina': 1,
        'hasConcierge': False,
        'yearBuilt': "",
        'unknownBuiltYear': True,
        'energyClass': "",
        'unknownEnergyClass': True,
        'knowExposition': False,
        'exposition': ['Est', 'Ovest'],
        'heating': "Centralizzato",
        'termovalvole': True,
        'unknownTermovalvole': False,
    }
    data['addressObject'] = {
        'address': 'Corso Svizzera',
        'streetNumber': 83,
        'city': 'Torino',
        'postalCode': 10143,
        'latitude': 45.083940,
        'longitude': 7.652170,
    }
    data['comparabili'].append({
        'airConditioning': False,
        'constructionYear': 1940,
        'elevator': True,
        'energyClass': 'A',
        'grossSquareFootage': 120,
        'latitude': 45.083940,
        'longitude': 7.652170,
        'maintenanceStatus': 'Da ristrutturare',
        'marketZone': '',
        'numberOfBathrooms': 1,
        'propertyType': 'Appartamento',
        'rooms': 3,
        'unitFloor': 1,
        'zipCode': 10143,
        'buildingFloorCount': 4,
        'gardenType': 'Comune',
        'terrace': False,
        'balcony': True,
        'furnished': 'non arredato',
        'kitchenType': 'cucina abitabile',
    })
    return data
