import unittest
from unittest.mock import patch, Mock
from mailjet import MailJ<PERSON>
from utils import dots_as_thousands_separator


class TestMailJet(unittest.TestCase):
    '''
    Class used to test mailjet.py
    '''

    def setUp(self) -> None:
        # set client attribute of self to be MailJet object at the beginning of every test
        self.client = MailJet(auth=('api_key', 'api_secret'), version='v3.1')
        # set useful vars for tests
        self.recipient_email = '<EMAIL>'
        self.recipient_name = 'Test User'
        self.args = {'date': '2022-01-01 12:00:00',
                     'address': '123 Main St',
                     'rooms': 3,
                     'squaredmeters': 100,
                     'predvalue': 100000}

    @property
    def data_new_prediction(self) -> dict:
        '''
        Property of the test object, data structure used in :code:`send_email_new_prediction()` method to send
        an email with template :code:`template_id = 4586168` used to infrom us of a new comparabile evaluation
        on the website. Data structure depends on MailJet's API and the data requested by our template.
        '''
        data = {
                'Messages': [
                    {
                        "From": {
                            "Email": '<EMAIL>',
                            "Name": 'NEWARC'
                        },
                        "To": [
                            {
                                "Email": self.recipient_email,
                                "Name": self.recipient_name
                            }
                        ],
                        "TemplateID": 4586168,
                        "TemplateLanguage": True,
                        "Subject": "Nuova valutazione immobile",
                        "Variables": {
                            'date': self.args['date'],
                            'address': self.args['address'],
                            'rooms': str(self.args['rooms']),
                            'squaredmeters': str(self.args['squaredmeters']),
                            'predvalue': dots_as_thousands_separator(self.args['predvalue'])
                        }
                    }
                ]
            }

        return data

    def test_send_email_new_prediction_correct_call(self) -> None:
        '''
        Tests :code:`send_email_new_prediction()` method from :code:`mailjet.MailJet` class.
        Correct call
        '''
        # we mock the method used to send an API request to MailJet
        with patch('mailjet_rest.client.Endpoint.create') as mock_send:
            print('\ntest_send_email_new_prediction_correct_call')
            # set mock response with expected return
            mock_response = Mock(status_code=200, json=lambda: {})
            mock_send.return_value = mock_response
            # record result from call to method tested
            result = self.client.send_email_new_prediction(self.recipient_email, self.recipient_name, **self.args)
            # assert that the mock was called exactly once and that call was with the specified arguments
            mock_send.assert_called_once_with(data=self.data_new_prediction)
            # assert status code equals expected values
            self.assertEqual(result.status_code, 200)
            self.assertEqual(result.json(), {})

    def test_send_email_new_prediction_bad_email(self) -> None:
        '''
        Tests :code:`send_email_new_prediction()` method from :code:`mailjet.MailJet` class.
        Bad email address
        '''
        # we mock the method used to send an API request to MailJet
        with patch('mailjet_rest.client.Endpoint.create') as mock_send:
            print('\ntest_send_email_new_prediction_bad_email')
            # set mock response with expected return
            mock_response = Mock(status_code=400, json=lambda: {})
            mock_send.return_value = mock_response
            # change email to invalid value
            self.recipient_email = 5
            # record result from call to method tested
            result = self.client.send_email_new_prediction(self.recipient_email, self.recipient_name, **self.args)
            # assert that the mock was called exactly once and that call was with the specified arguments
            mock_send.assert_called_once_with(data=self.data_new_prediction)
            # assert status code equals expected values
            self.assertEqual(result.status_code, 400)
            self.assertEqual(result.json(), {})

    def test_send_email_new_prediction_bad_name(self) -> None:
        '''
        Tests :code:`send_email_new_prediction()` method from :code:`mailjet.MailJet` class.
        Bad recipient's name
        '''
        with patch('mailjet_rest.client.Endpoint.create') as mock_send:
            print('\ntest_send_email_new_prediction_bad_name')
            # set mock response with expected return
            mock_response = Mock(status_code=400, json=lambda: {})
            mock_send.return_value = mock_response
            # change recipient's name to invalid format
            self.recipient_name = 7
            # record result from call to method tested
            result = self.client.send_email_new_prediction(self.recipient_email, self.recipient_name, **self.args)
            # assert that the mock was called exactly once and that call was with the specified arguments
            mock_send.assert_called_once_with(data=self.data_new_prediction)
            # assert status code equals expected values
            self.assertEqual(result.status_code, 400)
            self.assertEqual(result.json(), {})

    def test_send_email_new_prediction_no_template_vars(self) -> None:
        '''
        Tests :code:`send_email_new_prediction()` method from :code:`mailjet.MailJet` class.
        No template variables data
        '''
        with patch('mailjet_rest.client.Endpoint.create') as mock_send:
            print('\ntest_send_email_new_prediction_no_template_vars')
            # set mock response with expected return
            mock_response = Mock(status_code=400, json=lambda: {})
            mock_send.return_value = mock_response
            # change template data to invalid format
            for key in self.args:
                self.args[key] = None
            print(self.args)
            print(self.recipient_email)
            print(self.recipient_name)
            print(self.data_new_prediction)
            # record result from call to method tested
            result = self.client.send_email_new_prediction(self.recipient_email, self.recipient_name, **self.args)
            print(result.status_code)
            print(result)
            # assert that the mock was called exactly once and that call was with the specified arguments
            mock_send.assert_called_once_with(data=self.data_new_prediction)
            # assert status code equals expected values
            self.assertEqual(result.status_code, 400)
            self.assertEqual(result.json(), {})


if __name__ == '__main__':
    unittest.main()

