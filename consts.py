import os
from datetime import date
import json

# Tiresia API key
tiresia_api_key = os.environ.get('TIRESIA_API_KEY', 'UlisseOcioATornareACasa')

# MailJet API credentials
mailjet_api_key = os.environ.get('MAILJET_API_KEY')
mailjet_api_secret = os.environ.get('MAILJET_API_SECRET')
mailjet_auth = (mailjet_api_key, mailjet_api_secret)

# Google Places API credentials
gmaps_api_key = os.environ.get('GMAPS_API_KEY')

# OpenAI API key
openai_api_key = os.environ.get('OPENAI_API_KEY')
openai_api_key_immagina_smart: str = "***********************************************************************************************************************************************************************" #os.environ.get('OPENAI_API_KEY_IMMAGINA_SMART')

# DB credentials
DB_CONNECTION_NAME_READ = os.environ.get('DB_CONNECTION_NAME_READ')
DB_CONNECTION_NAME_WRITE = os.environ.get('DB_CONNECTION_NAME_WRITE')
# DB_USER = os.environ.get('DB_USER')
# DB_PASSWORD = os.environ.get('DB_PASSWORD')
DB_USER = 'root'
DB_PASSWORD = 'MuHcJ7cx49S!Mf5*'
DB_PORT = 3306
DB_NAME = 'prova_piattaforma'

DB_TABLE_NAME = 'immobiliare'
CONTACT_TABLE_NAME = 'contatti'
VENDUTO_TABLE_NAME = 'immobiliare_venduto'
EDIFICI_TABLE_NAME = 'immobiliare_edifici'

# DB_CONNECTION_NAME_READ_UNIX = "/cloudsql/newarc-staging:europe-west9:test-db"
# APP_SQL_GOOGLE_CREDENTIALS = os.environ.get('APP_SQL_GOOGLE_CREDENTIALS')
APP_SQL_GOOGLE_CREDENTIALS = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# SCRAPER_SQL_GOOGLE_CREDENTIALS = os.environ.get('SCRAPER_SQL_GOOGLE_CREDENTIALS')
SCRAPER_SQL_GOOGLE_CREDENTIALS = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Proxy configuration
# PROXY_CONFIG = json.loads(os.environ.get('PROXY_CONFIG'))
PROXY_CONFIG = {
    "url": "brd.superproxy.io",
    "port": "33335",
    "username": "brd-customer-hl_4949a02a-zone-scraping_immobiliare",
    "password": "d8vy0aj9fn1l"
}
PROXY_HOST = PROXY_CONFIG['url']
PROXY_PORT = PROXY_CONFIG['port']
PROXY_USER = PROXY_CONFIG['username']
PROXY_PASSWORD = PROXY_CONFIG['password']

# Date handling
TODAY = date.today().strftime('%Y/%m/%d')

# discr_cols: columns used to determine whether a comp is duplicate or not
DISCR_COLS = ['city',
              'grossSquareFootage',
              'numberOfBathrooms',
              'rooms',
              'unitFloor',
              'buildingFloorCount',
              'latitude',
              'longitude']

# Scraping immobiliare data format
IMMOBILIARE_SCRAPING_DICT = {'id': 'int not null auto_increment primary key',
                             'site': 'text',
                             'siteId': 'text',
                             'link': 'VARCHAR(100)',
                             'title': 'text',
                             'streetName': 'text',
                             'streetNum': 'text',
                             'city': 'text',
                             'zipCode': 'mediumint',
                             'marketZone': 'text',
                             'omiZone': 'text',
                             'latitude': 'double',
                             'longitude': 'double',
                             'propertyType': 'text',
                             'comparabileClass': 'text',
                             'maintenanceStatus': 'text',
                             'energyClass': 'text',
                             'constructionYear': 'smallint unsigned',
                             'grossSquareFootage': 'smallint unsigned',
                             'pricePsm': 'smallint unsigned',
                             'price': 'int unsigned',
                             'description': 'text',
                             'rooms': 'smallint unsigned',
                             'numberOfBathrooms': 'tinyint unsigned',
                             'unitFloor': 'text',
                             'buildingFloorCount': 'tinyint unsigned',
                             'elevator': 'bool',
                             'terrace': 'bool',
                             'balcony': 'bool',
                             'gardenType': 'text',
                             'kitchenType': 'text',
                             'furnished': 'text',
                             'hasCantina': 'bool',
                             'hasGarage': 'bool',
                             'hasConcierge': 'bool',
                             'numCarPlaces': 'tinyint unsigned',
                             'externalFrames': 'text',
                             'externalEsposition': 'text',
                             'airConditioning': 'bool',
                             'heatingType': 'text',
                             'heatingEmitter': 'text',
                             'heatingFuel': 'text',
                             'imagesUrls': 'text',
                             'planimetry': 'text',
                             'adInsertionDate': 'date',
                             'firstPrice': 'int unsigned',
                             'firstPriceDate': 'date',
                             'lastPriceDate': 'date',
                             'lastSeenDate': 'date',
                             'timesSeen': 'smallint unsigned',
                             'sellingAgency': 'text',
                             'sellingAgencyUrl': 'text',
                             'marketPricePsm': 'smallint unsigned',
                             'marketPriceDelta': 'decimal(4, 2)',
                             'renewedMarketPricePsm': 'smallint unsigned',
                             'offerPrice': 'int',
                             'minDiscount': 'decimal(5, 2)',
                             'sellingPrice': 'int unsigned',
                             'costs': 'int unsigned',
                             'grossMargin': 'int',
                             'predPricePsm': 'smallint unsigned',
                             'predPriceDelta': 'decimal(4, 2)',
                             'predRenewedPricePsm': 'smallint unsigned',
                             'predNewarcRenewedPricePsm': 'smallint unsigned',
                             'predOfferPrice': 'int',
                             'predMinDiscount': 'decimal(5, 2)',
                             'predSellingPrice': 'int unsigned',
                             'predCosts': 'int unsigned',
                             'predGrossMargin': 'int',
                             'newarcScore': 'tinyint unsigned',
                             'microzonaScore': 'tinyint unsigned',
                             'stabileScore': 'tinyint unsigned',
                             'immobileScore': 'tinyint unsigned',
                             'serviziScore': 'tinyint unsigned',
                             'velocitàScore': 'tinyint unsigned'}

# Contact MYSQL Data Format
CONTACT_TABLE_DICT = {'id': 'int not null auto_increment primary key',
                        'firebaseId': 'text',
                        'city': 'text',
                        'submitterName': 'text',
                        'submitterSurname': 'text',
                        'submitterEmail': 'text',
                        'submitterPhone': 'text',
                        'sellerType':  'text',
                        'insertionDate': 'date'}

VENDUTO_TABLE_DICT = {'id': 'int not null auto_increment primary key',
                        'city': 'text',
                        'site': 'text',
                        'siteId': 'text',
                        'firstSeenDate': 'date',
                        'lastSeenDate': 'date',
                        'timesSeen': 'smallint unsigned',
                        'sellingAgencyUrl': 'text'}

EDIFICI_TABLE_DICT = {'id': 'int not null auto_increment primary key',
                      'city': 'text',
                      'link': 'VARCHAR(100)',
                      'siteId': 'text',
                      'title': 'text',
                      'description': 'text',
                      'streetName': 'text',
                      'streetNum': 'text',
                      'propertyType': 'text',
                      'marketZone': 'text',
                      'omiZone': 'text',
                      'latitude': 'double',
                      'longitude': 'double',
                      'maintenanceStatus': 'text',
                      'rooms': 'smallint unsigned',
                      'numberOfBathrooms': 'tinyint unsigned',
                      'unitFloor': 'text',
                      'buildingFloorCount': 'tinyint unsigned',
                      'constructionYear': 'smallint unsigned',
                      'energyClass': 'text',
                      'heatingType': 'text',
                      'heatingEmitter': 'text',
                      'heatingFuel': 'text',
                      'airConditioning': 'bool',
                      'grossSquareFootage': 'smallint unsigned',
                      'price': 'int unsigned',
                      'pricePsm': 'smallint unsigned',
                      'firstPriceDate': 'date',
                      'lastPriceDate': 'date',
                      'lastSeenDate': 'date',
                      'sellingAgency': 'text',
                      'sellingAgencyUrl': 'text',
                      'timesSeen': 'smallint unsigned',
                      'imagesUrls': 'text',
                      'planimetry': 'text',
                      }

marketZones_list_dict = {
    'Torino': [
        "santa rita",
        "pozzo strada",
        "crocetta",
        "parella",
        "san salvario - dante",
        "san paolo",
        "barriera di milano",
        "borgo vittoria",
        "mirafiori sud - onorato vigliani",
        "cit turin",
        "cenisia",
        "san donato",
        "mirafiori nord",
        "madonna di campagna",
        "aurora",
        "lucento",
        "nizza millefonti",
        "via roma",
        "lingotto",
        "vanchiglietta",
        "quadrilatero romano",
        "campidoglio",
        "vanchiglia",
        "san secondo",
        "mirafiori sud - strada del drosso",
        "regio parco",
        "cittadella",
        "via della rocca",
        "rebaudengo",
        "san salvario - baretti",
        "centro europa",
        "le vallette",
        "gran madre - crimea",
        "cavoretto",
        "don bosco",
        "barriera di lanzo",
        "madonna del pilone",
        "piazza solferino",
        "giardini reali",
        "barca",
        "colle della maddalena",
        "falchera",
        "superga",
        "città giardino",
        "parco dora",
        "sassi",
        "bertolla",
        "italia 61",
    ],
    'Milano': [
        "città studi",
        "san siro",
        "centrale",
        "certosa",
        "sempione",
        "navigli - darsena",
        "buenos aires",
        "cenisio",
        "famagosta",
        "porta romana - medaglie d'oro",
        "dergano",
        "corvetto",
        "vigentino - fatima",
        "bovisa",
        "solari",
        "paolo sarpi",
        "ripamonti",
        "monte rosa - lotto",
        "maggiolina",
        "giambellino",
        "isola",
        "pasteur",
        "affori",
        "tre castelli - faenza",
        "indipendenza",
        "parco trotter",
        "ghisolfa - mac mahon",
        "lodi - brenta",
        "cimiano",
        "morgagni",
        "washington",
        "martini - insubria",
        "argonne - corsica",
        "porta venezia",
        "cadore",
        "casoretto",
        "farini",
        "vercelli - wagner",
        "cermenate - abbiategrasso",
        "pezzotti - meda",
        "plebisciti - susa",
        "precotto",
        "gallaratese",
        "cascina dei pomi",
        "corso genova",
        "crescenzago",
        "piave - tricolore",
        "moscova",
        "palestro",
        "turro",
        "ticinese",
        "bocconi",
        "gambara",
        "rovereto",
        "cantalupa - san paolo",
        "bande nere",
        "barona",
        "viale ungheria - mecenate",
        "tripoli - soderini",
        "baggio",
        "carrobbio",
        "cascina merlata - musocco",
        "niguarda",
        "ponte nuovo",
        "quadronno - crocetta",
        "porta vittoria",
        "montenero",
        "dezza",
        "piazzale siena",
        "repubblica",
        "piazza napoli",
        "corso san gottardo",
        "frua",
        "udine",
        "garibaldi - corso como",
        "guastalla",
        "porta nuova",
        "duomo",
        "bruzzano",
        "molise - cuoco",
        "amendola - buonarroti",
        "zara",
        "portello - parco vittoria",
        "vincenzo monti",
        "gorla",
        "istria",
        "quartiere adriano",
        "missori",
        "villa san giovanni",
        "ascanio sforza",
        "chiesa rossa",
        "bicocca",
        "quarto oggiaro",
        "de angeli",
        "lambrate",
        "brera",
        "comasina",
        "arena",
        "gratosoglio",
        "bisceglie",
        "greco - segnano",
        "pagano",
        "arco della pace",
        "melchiorre gioia",
        "inganni",
        "san carlo",
        "vialba",
        "turati",
        "santa giulia",
        "prato centenaro",
        "bologna - sulmona",
        "city life",
        "quartiere forlanini",
        "cadorna - castello",
        "primaticcio",
        "ponte lambro",
        "san vittore",
        "rubattino",
        "quintosole - chiaravalle",
        "lorenteggio",
        "bignami - ponale",
        "ca' granda",
        "ortica",
        "san babila",
        "lanza",
        "quartiere feltre",
        "sant'ambrogio",
        "quarto cagnino",
        "borgogna - largo augusto",
        "roserio",
        "trenno",
        "quadrilatero della moda",
        "figino",
        "quinto romano",
        "monte stella",
        "muggiano",
        "scala - manzoni",
        "bovisasca",
        "quartiere olmi",
        "rogoredo",
        "qt8",
        "cascina gobba",
        "parco lambro",
    ],
    'Roma': [
        "cinecittà",
        "torrevecchia",
        "alessandrino - torre spaccata",
        "talenti - monte sacro",
        "balduina",
        "centocelle",
        "borghesiana",
        "casalotti",
        "camilluccia - farnesina",
        "marconi",
        "tor sapienza - la rustica",
        "pisana - bravetta",
        "africano - villa chigi",
        "furio camillo",
        "infernetto",
        "pigneto",
        "acilia",
        "ponte di nona",
        "città giardino",
        "ardeatino - montagnola",
        "gregorio vii - piccolomini",
        "ostia ponente",
        "nuovo salario",
        "torre angela",
        "colli portuensi - casaletto",
        "ostia levante",
        "tomba di nerone",
        "bologna",
        "torrino",
        "monteverde nuovo",
        "cortina d'ampezzo",
        "vigna clara - vigna stelluti",
        "castelverde",
        "casal bruciato",
        "trullo - colle del sole",
        "fleming",
        "morena",
        "mazzini - delle vittorie",
        "san giovanni",
        "garbatella",
        "trastevere",
        "monteverde vecchio",
        "la storta",
        "trieste - coppedè",
        "parioli",
        "villa gordiani",
        "tor de' schiavi",
        "casal palocco",
        "magliana",
        "mezzocammino",
        "euclide",
        "battistini - primavalle",
        "tor tre teste - torre maura",
        "pietralata",
        "la giustiniana",
        "aurelio - val cannuta",
        "rocca cencia",
        "fonte ostiense",
        "fidene",
        "dragona",
        "flaminio",
        "portuense",
        "anagnina",
        "prati",
        "quadraro",
        "san paolo",
        "appio latino",
        "monte mario - trionfale",
        "tor vergata",
        "monti tiburtini",
        "castel di leva",
        "cecchignola - giuliano dalmata",
        "boccea",
        "ottavia",
        "mostacciano",
        "grottarossa - saxa rubra",
        "torpignattara",
        "conca d'oro - valli",
        "ponte mammolo",
        "cipro",
        "tintoretto",
        "baldo degli ubaldi",
        "eur",
        "casetta mattei - corviale",
        "re di roma",
        "esquilino",
        "finocchio",
        "romanina",
        "torre gaia",
        "prima porta",
        "san lorenzo",
        "casal bertone",
        "ostia antica",
        "collina delle muse",
        "villa bonelli",
        "san basilio",
        "villa fiorelli",
        "ponte galeria",
        "colli albani",
        "spinaceto",
        "colli aniene",
        "pinciano - villa ada",
        "tufello",
        "labaro",
        "cornelia - montespaccato",
        "porta di roma",
        "monti",
        "cassia - san godenzo",
        "axa",
        "giardinetti",
        "torresina",
        "appio claudio - statuario",
        "valle muricana",
        "policlinico",
        "olgiata",
        "trigoria",
        "salario - porta pia",
        "gemelli - pineta sacchetti",
        "fontana candida",
        "settebagni",
        "piazzale degli eroi",
        "spagna",
        "piazza del popolo",
        "corcolle",
        "fonte meravigliosa",
        "malafede",
        "vitinia",
        "settecamini",
        "massimina",
        "piazza navona",
        "ponte milvio",
        "rinnovamento",
        "sallustiano",
        "tor de' cenci",
        "colle oppio",
        "ostiense",
        "quarto miglio",
        "monte migliore",
        "casal lumbroso",
        "navigatori",
        "borgo",
        "cesano",
        "medaglie d'oro",
        "casal selce",
        "casal bernocchi",
        "bufalotta",
        "valle santa",
        "vallerano",
        "parco de' medici - muratella",
        "casal boccone",
        "repubblica",
        "arco di travertino",
        "piana del sole",
        "roma 70",
        "case rosse",
        "casal monastero",
        "san saba - caracalla",
        "serpentara",
        "colosseo - fori imperiali",
        "valle aurelia",
        "campo de' fiori",
        "testaccio",
        "montecitorio",
        "due ponti",
        "ghetto - portico d'ottavia",
        "via giulia",
        "osteria nuova",
        "castro pretorio",
        "centro giano",
        "tor cervara",
        "vigne nuove - casale nei",
        "pantheon",
        "vittorio veneto",
        "capannelle - statuario",
        "lunghezza",
        "fonte laurentina",
        "villaggio olimpico",
        "aventino",
        "barberini",
        "largo argentina",
        "appia pignatelli",
        "trevi",
        "castel di guido",
        "torricola - tor carbone",
        "castel fusano",
        "santa fumia",
        "san vittorino",
        "tor pagnotta",
        "maglianella",
        "divino amore",
        "tiberina",
        "fioranello",
        "falcognana",
        "pian savelli",
        "castel romano",
        "tor di quinto",
    ],
    'Trieste': [
        "borgo teresiano",
        "roiano",
        "san giacomo",
        "cologna - università",
        "san vito - campi elisi",
        "città vecchia",
        "settefontane",
        "largo barriera - ospedale maggiore",
        "baiamonti - valmaura",
        "barcola",
        "scorcola",
        "chiarbola - ponziana",
        "campanelle - costalunga",
        "san luigi - rozzol",
        "opicina",
        "gretta",
        "giardino pubblico",
        "costiera - grignano",
        "san giovanni",
        "sant'anna",
        "basovizza - trebiciano",
        "altura",
        "prosecco",
        "borgo san sergio",
        "santa croce",
        "longera",
        "conconello",
    ],
}
